-- 🔧 Fix Admin Access Issues - Version 2
-- Run this script in your Supabase SQL Editor

-- STEP 1: Drop current restrictive policies
DROP POLICY IF EXISTS "temp_allow_all_store_users" ON store_users;
DROP POLICY IF EXISTS "temp_allow_all_users" ON users;
DROP POLICY IF EXISTS "view_own_store_record" ON store_users;
DROP POLICY IF EXISTS "admins_view_all_store_users" ON store_users;
DROP POLICY IF EXISTS "admins_insert_store_users" ON store_users;
DROP POLICY IF EXISTS "admins_update_store_users" ON store_users;
DROP POLICY IF EXISTS "admins_delete_store_users" ON store_users;
DROP POLICY IF EXISTS "authenticated_view_users" ON users;
DROP POLICY IF EXISTS "admins_insert_users" ON users;
DROP POLICY IF EXISTS "admins_update_users" ON users;
DROP POLICY IF EXISTS "admins_delete_users" ON users;

-- STEP 2: Create simple, working policies
-- For store_users: Allow all operations for authenticated users (temporary)
CREATE POLICY "allow_authenticated_store_users" ON store_users
    FOR ALL USING (auth.role() = 'authenticated');

-- For users: Allow all operations for authenticated users (temporary)  
CREATE POLICY "allow_authenticated_users" ON users
    FOR ALL USING (auth.role() = 'authenticated');

-- STEP 3: Ensure admin user exists in store_users
-- First, let's find the admin user ID from auth.users
DO $$
DECLARE
    admin_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Find the admin user by email
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    -- If admin user exists, ensure they have store access
    IF admin_user_id IS NOT NULL THEN
        -- Insert admin user into store_users if not exists
        INSERT INTO store_users (id, user_id, store_id, role, created_at, updated_at)
        VALUES (
            gen_random_uuid(),
            admin_user_id,
            womanza_store_id,
            'admin',
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id, store_id) DO UPDATE SET
            role = 'admin',
            updated_at = NOW();
            
        RAISE NOTICE 'Admin user access ensured for: %', admin_user_id;
    ELSE
        RAISE NOTICE 'Admin user not found with email: <EMAIL>';
    END IF;
    
    -- Also check for super admin email
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    IF admin_user_id IS NOT NULL THEN
        -- Insert super admin user into store_users if not exists
        INSERT INTO store_users (id, user_id, store_id, role, created_at, updated_at)
        VALUES (
            gen_random_uuid(),
            admin_user_id,
            womanza_store_id,
            'super_admin',
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id, store_id) DO UPDATE SET
            role = 'super_admin',
            updated_at = NOW();
            
        RAISE NOTICE 'Super admin user access ensured for: %', admin_user_id;
    ELSE
        RAISE NOTICE 'Super admin user not found with email: <EMAIL>';
    END IF;
END $$;

-- STEP 4: Show current status
SELECT 'Current store_users:' as info;
SELECT su.id, su.user_id, au.email, su.role, su.created_at 
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY su.created_at;

-- Success message
SELECT 'Admin access fixed! Please refresh your admin panel.' as status;
