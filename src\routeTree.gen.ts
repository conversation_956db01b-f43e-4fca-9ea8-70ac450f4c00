/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as UsersRouteImport } from './routes/users'
import { Route as StoreRouteImport } from './routes/store'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as ProductsRouteImport } from './routes/products'
import { Route as OrdersRouteImport } from './routes/orders'
import { Route as MarketingRouteImport } from './routes/marketing'
import { Route as LoginRouteImport } from './routes/login'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as CustomersRouteImport } from './routes/customers'
import { Route as CatalogRouteImport } from './routes/catalog'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ProductsNewRouteImport } from './routes/products.new'
import { Route as ProductsInventoryRouteImport } from './routes/products.inventory'
import { Route as ProductsCategoriesRouteImport } from './routes/products.categories'
import { Route as CatalogInventoryRouteImport } from './routes/catalog.inventory'
import { Route as CatalogCategoriesRouteImport } from './routes/catalog.categories'
import { Route as CatalogAddRouteImport } from './routes/catalog.add'

const UsersRoute = UsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => rootRouteImport,
} as any)
const StoreRoute = StoreRouteImport.update({
  id: '/store',
  path: '/store',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsRoute = ProductsRouteImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => rootRouteImport,
} as any)
const OrdersRoute = OrdersRouteImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => rootRouteImport,
} as any)
const MarketingRoute = MarketingRouteImport.update({
  id: '/marketing',
  path: '/marketing',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomersRoute = CustomersRouteImport.update({
  id: '/customers',
  path: '/customers',
  getParentRoute: () => rootRouteImport,
} as any)
const CatalogRoute = CatalogRouteImport.update({
  id: '/catalog',
  path: '/catalog',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsNewRoute = ProductsNewRouteImport.update({
  id: '/new',
  path: '/new',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsInventoryRoute = ProductsInventoryRouteImport.update({
  id: '/inventory',
  path: '/inventory',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsCategoriesRoute = ProductsCategoriesRouteImport.update({
  id: '/categories',
  path: '/categories',
  getParentRoute: () => ProductsRoute,
} as any)
const CatalogInventoryRoute = CatalogInventoryRouteImport.update({
  id: '/inventory',
  path: '/inventory',
  getParentRoute: () => CatalogRoute,
} as any)
const CatalogCategoriesRoute = CatalogCategoriesRouteImport.update({
  id: '/categories',
  path: '/categories',
  getParentRoute: () => CatalogRoute,
} as any)
const CatalogAddRoute = CatalogAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => CatalogRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/catalog': typeof CatalogRouteWithChildren
  '/customers': typeof CustomersRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/marketing': typeof MarketingRoute
  '/orders': typeof OrdersRoute
  '/products': typeof ProductsRouteWithChildren
  '/settings': typeof SettingsRoute
  '/store': typeof StoreRoute
  '/users': typeof UsersRoute
  '/catalog/add': typeof CatalogAddRoute
  '/catalog/categories': typeof CatalogCategoriesRoute
  '/catalog/inventory': typeof CatalogInventoryRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/catalog': typeof CatalogRouteWithChildren
  '/customers': typeof CustomersRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/marketing': typeof MarketingRoute
  '/orders': typeof OrdersRoute
  '/products': typeof ProductsRouteWithChildren
  '/settings': typeof SettingsRoute
  '/store': typeof StoreRoute
  '/users': typeof UsersRoute
  '/catalog/add': typeof CatalogAddRoute
  '/catalog/categories': typeof CatalogCategoriesRoute
  '/catalog/inventory': typeof CatalogInventoryRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/catalog': typeof CatalogRouteWithChildren
  '/customers': typeof CustomersRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/marketing': typeof MarketingRoute
  '/orders': typeof OrdersRoute
  '/products': typeof ProductsRouteWithChildren
  '/settings': typeof SettingsRoute
  '/store': typeof StoreRoute
  '/users': typeof UsersRoute
  '/catalog/add': typeof CatalogAddRoute
  '/catalog/categories': typeof CatalogCategoriesRoute
  '/catalog/inventory': typeof CatalogInventoryRoute
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/catalog'
    | '/customers'
    | '/dashboard'
    | '/login'
    | '/marketing'
    | '/orders'
    | '/products'
    | '/settings'
    | '/store'
    | '/users'
    | '/catalog/add'
    | '/catalog/categories'
    | '/catalog/inventory'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/catalog'
    | '/customers'
    | '/dashboard'
    | '/login'
    | '/marketing'
    | '/orders'
    | '/products'
    | '/settings'
    | '/store'
    | '/users'
    | '/catalog/add'
    | '/catalog/categories'
    | '/catalog/inventory'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  id:
    | '__root__'
    | '/'
    | '/catalog'
    | '/customers'
    | '/dashboard'
    | '/login'
    | '/marketing'
    | '/orders'
    | '/products'
    | '/settings'
    | '/store'
    | '/users'
    | '/catalog/add'
    | '/catalog/categories'
    | '/catalog/inventory'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CatalogRoute: typeof CatalogRouteWithChildren
  CustomersRoute: typeof CustomersRoute
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  MarketingRoute: typeof MarketingRoute
  OrdersRoute: typeof OrdersRoute
  ProductsRoute: typeof ProductsRouteWithChildren
  SettingsRoute: typeof SettingsRoute
  StoreRoute: typeof StoreRoute
  UsersRoute: typeof UsersRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/users': {
      id: '/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/store': {
      id: '/store'
      path: '/store'
      fullPath: '/store'
      preLoaderRoute: typeof StoreRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products': {
      id: '/products'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/orders': {
      id: '/orders'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof OrdersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/marketing': {
      id: '/marketing'
      path: '/marketing'
      fullPath: '/marketing'
      preLoaderRoute: typeof MarketingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/customers': {
      id: '/customers'
      path: '/customers'
      fullPath: '/customers'
      preLoaderRoute: typeof CustomersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/catalog': {
      id: '/catalog'
      path: '/catalog'
      fullPath: '/catalog'
      preLoaderRoute: typeof CatalogRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products/new': {
      id: '/products/new'
      path: '/new'
      fullPath: '/products/new'
      preLoaderRoute: typeof ProductsNewRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/inventory': {
      id: '/products/inventory'
      path: '/inventory'
      fullPath: '/products/inventory'
      preLoaderRoute: typeof ProductsInventoryRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/categories': {
      id: '/products/categories'
      path: '/categories'
      fullPath: '/products/categories'
      preLoaderRoute: typeof ProductsCategoriesRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/catalog/inventory': {
      id: '/catalog/inventory'
      path: '/inventory'
      fullPath: '/catalog/inventory'
      preLoaderRoute: typeof CatalogInventoryRouteImport
      parentRoute: typeof CatalogRoute
    }
    '/catalog/categories': {
      id: '/catalog/categories'
      path: '/categories'
      fullPath: '/catalog/categories'
      preLoaderRoute: typeof CatalogCategoriesRouteImport
      parentRoute: typeof CatalogRoute
    }
    '/catalog/add': {
      id: '/catalog/add'
      path: '/add'
      fullPath: '/catalog/add'
      preLoaderRoute: typeof CatalogAddRouteImport
      parentRoute: typeof CatalogRoute
    }
  }
}

interface CatalogRouteChildren {
  CatalogAddRoute: typeof CatalogAddRoute
  CatalogCategoriesRoute: typeof CatalogCategoriesRoute
  CatalogInventoryRoute: typeof CatalogInventoryRoute
}

const CatalogRouteChildren: CatalogRouteChildren = {
  CatalogAddRoute: CatalogAddRoute,
  CatalogCategoriesRoute: CatalogCategoriesRoute,
  CatalogInventoryRoute: CatalogInventoryRoute,
}

const CatalogRouteWithChildren =
  CatalogRoute._addFileChildren(CatalogRouteChildren)

interface ProductsRouteChildren {
  ProductsCategoriesRoute: typeof ProductsCategoriesRoute
  ProductsInventoryRoute: typeof ProductsInventoryRoute
  ProductsNewRoute: typeof ProductsNewRoute
}

const ProductsRouteChildren: ProductsRouteChildren = {
  ProductsCategoriesRoute: ProductsCategoriesRoute,
  ProductsInventoryRoute: ProductsInventoryRoute,
  ProductsNewRoute: ProductsNewRoute,
}

const ProductsRouteWithChildren = ProductsRoute._addFileChildren(
  ProductsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CatalogRoute: CatalogRouteWithChildren,
  CustomersRoute: CustomersRoute,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  MarketingRoute: MarketingRoute,
  OrdersRoute: OrdersRoute,
  ProductsRoute: ProductsRouteWithChildren,
  SettingsRoute: SettingsRoute,
  StoreRoute: StoreRoute,
  UsersRoute: UsersRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
