import { UseFormReturn } from 'react-hook-form'
import type { ProductFormData } from '@/types/product'

interface ProductSEOProps {
  form: UseFormReturn<ProductFormData>
}

export function ProductSEO({ form }: ProductSEOProps) {
  const { register, watch, setValue } = form

  const productName = watch('name')
  const metaTitle = watch('meta_title')
  const metaDescription = watch('meta_description')
  const slug = watch('slug')

  const generateMetaTitle = () => {
    if (productName) {
      setValue('meta_title', `${productName} | Womanza Jewelry`)
    }
  }

  const generateMetaDescription = () => {
    if (productName) {
      const metalType = watch('metal_type')
      const gemstoneType = watch('gemstone_type')
      
      let description = `Shop ${productName}`
      if (metalType) description += ` in ${metalType}`
      if (gemstoneType) description += ` with ${gemstoneType}`
      description += '. High-quality jewelry with free shipping and returns. Order now!'
      
      setValue('meta_description', description)
    }
  }

  const metaTitleLength = metaTitle?.length || 0
  const metaDescriptionLength = metaDescription?.length || 0

  const getTitleStatus = () => {
    if (metaTitleLength === 0) return { color: 'text-gray-500', text: 'No title set' }
    if (metaTitleLength < 30) return { color: 'text-orange-600', text: 'Too short' }
    if (metaTitleLength > 60) return { color: 'text-red-600', text: 'Too long' }
    return { color: 'text-green-600', text: 'Good length' }
  }

  const getDescriptionStatus = () => {
    if (metaDescriptionLength === 0) return { color: 'text-gray-500', text: 'No description set' }
    if (metaDescriptionLength < 120) return { color: 'text-orange-600', text: 'Too short' }
    if (metaDescriptionLength > 160) return { color: 'text-red-600', text: 'Too long' }
    return { color: 'text-green-600', text: 'Good length' }
  }

  const titleStatus = getTitleStatus()
  const descriptionStatus = getDescriptionStatus()

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          SEO & Metadata
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Optimize your product for search engines to improve visibility and click-through rates.
        </p>
      </div>

      {/* Search Preview */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          Search Engine Preview
        </h4>
        
        <div className="bg-white border rounded-lg p-4 font-sans">
          <div className="text-blue-600 text-lg hover:underline cursor-pointer">
            {metaTitle || productName || 'Product Title'}
          </div>
          <div className="text-green-700 text-sm mt-1">
            https://womanza.com/products/{slug || 'product-slug'}
          </div>
          <div className="text-gray-600 text-sm mt-2 leading-relaxed">
            {metaDescription || 'Product description will appear here. This is what customers will see in search results.'}
          </div>
        </div>
      </div>

      {/* Meta Title */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-admin-text-primary">
            Meta Title
          </label>
          <button
            type="button"
            onClick={generateMetaTitle}
            className="text-xs text-admin-primary hover:text-admin-primary/80"
          >
            Auto-generate
          </button>
        </div>
        
        <input
          type="text"
          {...register('meta_title')}
          className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          placeholder="Enter meta title for search engines..."
        />
        
        <div className="flex items-center justify-between mt-2">
          <p className={`text-xs ${titleStatus.color}`}>
            {titleStatus.text}
          </p>
          <p className={`text-xs ${metaTitleLength > 60 ? 'text-red-600' : 'text-admin-text-secondary'}`}>
            {metaTitleLength}/60 characters
          </p>
        </div>
        
        <p className="text-xs text-admin-text-secondary mt-1">
          Appears as the clickable headline in search results. Keep it under 60 characters.
        </p>
      </div>

      {/* Meta Description */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-admin-text-primary">
            Meta Description
          </label>
          <button
            type="button"
            onClick={generateMetaDescription}
            className="text-xs text-admin-primary hover:text-admin-primary/80"
          >
            Auto-generate
          </button>
        </div>
        
        <textarea
          {...register('meta_description')}
          rows={3}
          className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          placeholder="Enter meta description for search engines..."
        />
        
        <div className="flex items-center justify-between mt-2">
          <p className={`text-xs ${descriptionStatus.color}`}>
            {descriptionStatus.text}
          </p>
          <p className={`text-xs ${metaDescriptionLength > 160 ? 'text-red-600' : 'text-admin-text-secondary'}`}>
            {metaDescriptionLength}/160 characters
          </p>
        </div>
        
        <p className="text-xs text-admin-text-secondary mt-1">
          Appears below the title in search results. Keep it between 120-160 characters.
        </p>
      </div>

      {/* URL Slug */}
      <div>
        <label className="block text-sm font-medium text-admin-text-primary mb-2">
          URL Slug
        </label>
        <div className="flex items-center">
          <span className="text-sm text-admin-text-secondary bg-admin-bg-primary border border-r-0 border-admin-border rounded-l-md px-3 py-2">
            /products/
          </span>
          <input
            type="text"
            {...register('slug')}
            className="flex-1 px-3 py-2 border border-admin-border rounded-r-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            placeholder="product-url-slug"
          />
        </div>
        <p className="text-xs text-admin-text-secondary mt-1">
          URL-friendly version of the product name. Use lowercase letters, numbers, and hyphens only.
        </p>
      </div>

      {/* SEO Checklist */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          SEO Checklist
        </h4>
        
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${productName ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-admin-text-primary">Product name is descriptive</span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${metaTitle && metaTitleLength >= 30 && metaTitleLength <= 60 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-admin-text-primary">Meta title is optimized (30-60 characters)</span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${metaDescription && metaDescriptionLength >= 120 && metaDescriptionLength <= 160 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-admin-text-primary">Meta description is optimized (120-160 characters)</span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${slug && slug.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-admin-text-primary">URL slug is SEO-friendly</span>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${watch('images')?.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-admin-text-primary">Product has images</span>
          </div>
        </div>
      </div>

      {/* SEO Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          🔍 SEO Best Practices
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Include target keywords naturally in title and description</li>
          <li>• Write for humans first, search engines second</li>
          <li>• Use specific product details (metal type, gemstone, etc.)</li>
          <li>• Avoid keyword stuffing - it hurts rankings</li>
          <li>• Make titles and descriptions compelling to increase clicks</li>
          <li>• Keep URLs short and descriptive</li>
        </ul>
      </div>
    </div>
  )
}
