-- Simple Users Table Setup for Womanza Admin Panel
-- Run this in your Supabase SQL Editor

-- Create users table for storing user details
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table (store users can view users in their store)
CREATE POLICY "Store users can view users in their store" ON users
    FOR SELECT USING (
        id IN (
            SELECT su.user_id 
            FROM store_users su 
            WHERE su.store_id IN (
                SELECT store_id 
                FROM store_users 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create policy for inserting users (only super_admin and admin)
CREATE POLICY "Store admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Create updated_at trigger for users
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Update store_users table to ensure it has proper structure
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Create trigger for store_users updated_at
CREATE TRIGGER update_store_users_updated_at 
    BEFORE UPDATE ON store_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
