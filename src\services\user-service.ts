import { supabase } from '@/lib/supabase'
import { UserRole } from '@/types/user'

// Define StoreUser interface locally
interface StoreUser {
  id: string
  user_id: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  status: string
  created_at: string
  updated_at?: string
}

// Define permission check function locally
function canInviteUser(currentRole: UserRole, targetRole: UserRole): boolean {
  // Super admin can create any role
  if (currentRole === 'super_admin') return true

  // Admin can create editor and viewer (not admin)
  if (currentRole === 'admin') {
    return ['editor', 'viewer'].includes(targetRole)
  }

  // Editor and viewer cannot create users
  return false
}

const WOMANZA_STORE = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  name: 'Womanza Jewelry Store'
}

export interface CreateUserData {
  email: string
  firstName: string
  lastName: string
  password: string
  role: UserRole
}

export interface InviteResponse {
  success: boolean
  data?: any
  error?: string
}

export class UserService {
  /**
   * Get all active users for the store
   */
  static async getAllUsers(): Promise<StoreUser[]> {
    try {
      // Get all store_users for this store
      const { data: storeUsers, error: storeUsersError } = await supabase
        .from('store_users')
        .select('*')
        .eq('store_id', WOMANZA_STORE.id)
        .order('created_at', { ascending: false })

      if (storeUsersError) throw storeUsersError

      if (!storeUsers || storeUsers.length === 0) {
        return []
      }

      // Get user details from activity logs
      const userIds = storeUsers.map(su => su.user_id)
      const { data: userProfiles, error: profilesError } = await supabase
        .from('user_activity_logs')
        .select('*')
        .eq('store_id', WOMANZA_STORE.id)
        .eq('action', 'user_profile_created')
        .in('details->target_user_id', userIds)

      if (profilesError) {
        console.error('Error fetching user profiles from activity logs:', profilesError)
      }

      // Combine store_users with user profile details from activity logs
      return storeUsers.map(storeUser => {
        const profile = userProfiles?.find(p => p.details?.target_user_id === storeUser.user_id)

        return {
          id: storeUser.id,
          user_id: storeUser.user_id,
          email: profile?.details?.email || 'Unknown',
          first_name: profile?.details?.first_name || 'Unknown',
          last_name: profile?.details?.last_name || 'User',
          role: storeUser.role as UserRole,
          status: 'active', // Always active for simplicity
          created_at: storeUser.created_at,
          updated_at: storeUser.updated_at
        }
      })
    } catch (error) {
      console.error('Error fetching users:', error)
      return []
    }
  }



  /**
   * Get user activity logs
   */
  static async getUserActivityLogs(limit: number = 50) {
    try {
      // Get activity logs
      const { data: logs, error: logsError } = await supabase
        .from('user_activity_logs')
        .select('*')
        .eq('store_id', WOMANZA_STORE.id)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (logsError) throw logsError

      if (!logs || logs.length === 0) {
        return []
      }

      // Get unique user IDs
      const userIds = [...new Set(logs.map(log => log.user_id).filter(Boolean))]

      if (userIds.length === 0) {
        return logs.map(log => ({
          ...log,
          user: null
        }))
      }

      // Get user details
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, email, first_name, last_name')
        .in('id', userIds)

      if (usersError) throw usersError

      // Combine the data
      return logs.map(log => ({
        ...log,
        user: users?.find(u => u.id === log.user_id) || null
      }))
    } catch (error) {
      console.error('Error fetching activity logs:', error)
      return []
    }
  }

  /**
   * Create a new user directly (replaces invitation system)
   */
  static async createUser(
    userData: CreateUserData,
    currentUserRole: UserRole
  ): Promise<InviteResponse> {
    try {
      // Check if current user can create this role
      if (!canInviteUser(currentUserRole, userData.role)) {
        return {
          success: false,
          error: 'You do not have permission to create users with this role'
        }
      }

      // Validate input
      if (!userData.email || !userData.firstName || !userData.lastName || !userData.password || !userData.role) {
        return {
          success: false,
          error: 'All fields are required'
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        }
      }

      // Validate password strength
      if (userData.password.length < 6) {
        return {
          success: false,
          error: 'Password must be at least 6 characters long'
        }
      }

      // Get current user for logging
      const { data: { user: currentUser } } = await supabase.auth.getUser()

      // Generate a unique user ID for manual creation
      const newUserId = crypto.randomUUID()

      // Create store_users record with minimal fields only
      const { error: storeUserError } = await supabase
        .from('store_users')
        .insert({
          store_id: WOMANZA_STORE.id,
          user_id: newUserId,
          role: userData.role
        })

      if (storeUserError) {
        console.error('Store user insert error details:', {
          error: storeUserError,
          code: storeUserError.code,
          message: storeUserError.message,
          details: storeUserError.details,
          hint: storeUserError.hint
        })
        return {
          success: false,
          error: `Failed to create user: ${storeUserError.message || 'Unknown error'}`
        }
      }

      // Store user details in user_activity_logs table as a workaround
      // This allows us to track user details in the database
      await this.logUserActivity('user_profile_created', {
        target_user_id: newUserId,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        role: userData.role,
        created_by: currentUser?.id,
        user_creation_timestamp: new Date().toISOString()
      })

      // Log activity
      await this.logUserActivity('user_created', {
        target_user_id: newUserId,
        target_email: userData.email,
        target_role: userData.role,
        target_name: `${userData.firstName} ${userData.lastName}`,
        created_by: currentUser?.id
      })

      return {
        success: true,
        data: {
          id: newUserId,
          email: userData.email,
          role: userData.role,
          status: 'active',
          first_name: userData.firstName,
          last_name: userData.lastName,
          message: `User ${userData.firstName} ${userData.lastName} created successfully`
        }
      }
    } catch (error) {
      console.error('Error creating user:', error)
      return {
        success: false,
        error: 'Failed to create user'
      }
    }
  }

  /**
   * Update user role
   */
  static async updateUserRole(
    userId: string,
    newRole: UserRole,
    currentUserRole: UserRole
  ): Promise<InviteResponse> {
    try {
      // Check if current user can update this role
      if (!canInviteUser(currentUserRole, newRole)) {
        return {
          success: false,
          error: 'You do not have permission to assign this role'
        }
      }

      // Update the role in store_users table
      const { error } = await supabase
        .from('store_users')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('store_id', WOMANZA_STORE.id)

      if (error) {
        console.error('Error updating user role:', error)
        return {
          success: false,
          error: 'Failed to update user role'
        }
      }

      // Log the activity
      await this.logUserActivity('role_updated', {
        target_user_id: userId,
        new_role: newRole,
        old_role: 'unknown'
      })

      return {
        success: true,
        data: { message: 'User role updated successfully' }
      }
    } catch (error) {
      console.error('Error updating user role:', error)
      return {
        success: false,
        error: 'Failed to update user role'
      }
    }
  }

  /**
   * Deactivate a user
   */
  static async deactivateUser(
    userId: string,
    currentUserRole: UserRole
  ): Promise<InviteResponse> {
    try {
      // Get the target user's role first
      const { data: storeUser, error: fetchError } = await supabase
        .from('store_users')
        .select('role')
        .eq('user_id', userId)
        .eq('store_id', WOMANZA_STORE.id)
        .single()

      if (fetchError || !storeUser) {
        return {
          success: false,
          error: 'User not found'
        }
      }

      // Check if current user can deactivate this user
      const { allowed } = this.canDeleteTargetUser(currentUserRole, storeUser.role as UserRole, false)
      if (!allowed) {
        return {
          success: false,
          error: 'You do not have permission to deactivate this user'
        }
      }

      // Update the status to inactive
      const { error } = await supabase
        .from('store_users')
        .update({ status: 'inactive', updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('store_id', WOMANZA_STORE.id)

      if (error) {
        console.error('Error deactivating user:', error)
        return {
          success: false,
          error: 'Failed to deactivate user'
        }
      }

      // Log the activity
      await this.logUserActivity('user_deactivated', {
        target_user_id: userId,
        target_role: storeUser.role
      })

      return {
        success: true,
        data: { message: 'User deactivated successfully' }
      }
    } catch (error) {
      console.error('Error deactivating user:', error)
      return {
        success: false,
        error: 'Failed to deactivate user'
      }
    }
  }

  /**
   * Check if current user can delete target user
   */
  static canDeleteTargetUser(
    currentRole: UserRole | null,
    targetRole: UserRole,
    isSelf: boolean
  ): { allowed: boolean; reason?: string } {
    if (!currentRole) {
      return { allowed: false, reason: 'No role assigned' }
    }

    // Cannot delete yourself
    if (isSelf) {
      return { allowed: false, reason: 'Cannot delete yourself' }
    }

    // Cannot delete super admin
    if (targetRole === 'super_admin') {
      return { allowed: false, reason: 'Cannot delete super admin' }
    }

    // Super admin can delete anyone except super admin
    if (currentRole === 'super_admin') {
      return { allowed: true }
    }

    // Admin can delete editor and viewer
    if (currentRole === 'admin') {
      if (['editor', 'viewer'].includes(targetRole)) {
        return { allowed: true }
      }
      return { allowed: false, reason: 'Cannot delete admin or super admin' }
    }

    // Editor and viewer cannot delete anyone
    return { allowed: false, reason: 'Insufficient permissions' }
  }

  /**
   * Log user activity
   */
  static async logUserActivity(action: string, details: any): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      await supabase
        .from('user_activity_logs')
        .insert({
          store_id: WOMANZA_STORE.id,
          user_id: user.id,
          action,
          details,
          ip_address: await this.getClientIP(),
          user_agent: navigator.userAgent
        })
    } catch (error) {
      console.error('Error logging user activity:', error)
    }
  }

  /**
   * Get client IP address
   */
  static async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json')
      const data = await response.json()
      return data.ip
    } catch (error) {
      return 'unknown'
    }
  }
}
