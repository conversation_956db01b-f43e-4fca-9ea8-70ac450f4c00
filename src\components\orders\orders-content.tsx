import React, { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  ClockIcon,
  TruckIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { ORDER_STATUSES } from '@/lib/constants'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import toast from 'react-hot-toast'

interface Order {
  id: string
  order_number: string
  customer_name: string
  customer_email: string
  total_amount: number
  status: keyof typeof ORDER_STATUSES
  created_at: string
  items_count: number
}

export function OrdersContent() {
  const { hasPermission } = useStore()
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // TODO: Implement real order fetching from Supabase
  useEffect(() => {
    setLoading(false)
    setOrders([])
  }, [])

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer_email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusIcon = (status: keyof typeof ORDER_STATUSES) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4" />
      case 'processing':
        return <ClockIcon className="h-4 w-4" />
      case 'shipped':
        return <TruckIcon className="h-4 w-4" />
      case 'delivered':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    if (!hasPermission('update_order_status')) {
      toast.error('You do not have permission to update order status')
      return
    }

    // Simulate API call
    toast.success('Order status updated successfully')
    
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus as keyof typeof ORDER_STATUSES }
        : order
    ))
  }

  if (!hasPermission('view_orders')) {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
        <h3 className="text-lg font-medium text-admin-text-primary mb-2">
          Access Denied
        </h3>
        <p className="text-admin-text-muted">
          You don't have permission to view orders.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-admin-text-primary">
            Orders
          </h1>
          <p className="text-admin-text-muted mt-1">
            Manage customer orders and track fulfillment
          </p>
        </div>
        {hasPermission('manage_orders') && (
          <Button className="flex items-center gap-2">
            <PlusIcon className="h-4 w-4" />
            New Order
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-admin-text-muted" />
            <Input
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-admin-border rounded-md bg-admin-bg-secondary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
        >
          <option value="all">All Status</option>
          {Object.entries(ORDER_STATUSES).map(([key, status]) => (
            <option key={key} value={key}>
              {status.label}
            </option>
          ))}
        </select>
        <Button variant="ghost" className="flex items-center gap-2">
          <FunnelIcon className="h-4 w-4" />
          More Filters
        </Button>
      </div>

      {/* Orders Table */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-primary mx-auto"></div>
          <p className="text-admin-text-muted mt-2">Loading orders...</p>
        </div>
      ) : (
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Order
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Total
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-admin-border">
                {filteredOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-admin-hover">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-admin-text-primary">
                          {order.order_number}
                        </div>
                        <div className="text-sm text-admin-text-muted">
                          {order.items_count} item{order.items_count !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-admin-text-primary">
                          {order.customer_name}
                        </div>
                        <div className="text-sm text-admin-text-muted">
                          {order.customer_email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full gap-1 ${
                        order.status === 'pending' ? 'bg-admin-warning/10 text-admin-warning' :
                        order.status === 'processing' ? 'bg-admin-info/10 text-admin-info' :
                        order.status === 'shipped' ? 'bg-admin-primary/10 text-admin-primary' :
                        order.status === 'delivered' ? 'bg-admin-success/10 text-admin-success' :
                        'bg-admin-error/10 text-admin-error'
                      }`}>
                        {getStatusIcon(order.status)}
                        {ORDER_STATUSES[order.status].label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-admin-text-primary">
                      ${order.total_amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-muted">
                      {new Date(order.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          className="text-admin-text-muted hover:text-admin-text-primary"
                          title="View order"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {hasPermission('update_order_status') && (
                          <select
                            value={order.status}
                            onChange={(e) => handleStatusUpdate(order.id, e.target.value)}
                            className="text-xs border border-admin-border rounded px-2 py-1 bg-admin-bg-primary"
                          >
                            {Object.entries(ORDER_STATUSES).map(([key, status]) => (
                              <option key={key} value={key}>
                                {status.label}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {filteredOrders.length === 0 && !loading && (
        <div className="text-center py-12">
          <ClockIcon className="h-12 w-12 text-admin-text-muted mx-auto mb-4" />
          <h3 className="text-lg font-medium text-admin-text-primary mb-2">
            No orders found
          </h3>
          <p className="text-admin-text-muted">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Orders will appear here once customers start placing them'
            }
          </p>
        </div>
      )}
    </div>
  )
}
