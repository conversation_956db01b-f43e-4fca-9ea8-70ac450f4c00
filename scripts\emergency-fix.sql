-- 🚨 EMERGENCY FIX - Stop Infinite Recursion NOW
-- Run this script immediately in your Supabase SQL Editor

-- STEP 1: Disable <PERSON><PERSON> temporarily to stop the recursion
ALTER TABLE store_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- STEP 2: Drop ALL policies completely
DROP POLICY IF EXISTS "Users can view own store record" ON store_users;
DROP POLICY IF EXISTS "Admins can manage store users" ON store_users;
DROP POLICY IF EXISTS "Users can view own store users" ON store_users;
DROP POLICY IF EXISTS "Store admins can manage store users" ON store_users;
DROP POLICY IF EXISTS "Allow read for authenticated users" ON users;
DROP POLICY IF EXISTS "Allow insert for admins" ON users;
DROP POLICY IF EXISTS "Allow update for admins" ON users;
DROP POLICY IF EXISTS "Allow all for authenticated users" ON users;

-- Drop any remaining policies programmatically
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    -- Drop all policies on store_users
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'store_users'
    LOOP
        EXECUTE 'DROP POLICY "' || policy_name || '" ON store_users';
    END LOOP;
    
    -- Drop all policies on users
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'users'
    LOOP
        EXECUTE 'DROP POLICY "' || policy_name || '" ON users';
    END LOOP;
END $$;

-- STEP 3: Create SIMPLE, NON-RECURSIVE policies
-- Re-enable RLS
ALTER TABLE store_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- For store_users: Allow all operations for authenticated users (temporary)
CREATE POLICY "temp_allow_all_store_users" ON store_users
    FOR ALL USING (auth.role() = 'authenticated');

-- For users: Allow all operations for authenticated users (temporary)
CREATE POLICY "temp_allow_all_users" ON users
    FOR ALL USING (auth.role() = 'authenticated');

-- Success message
SELECT 'Emergency fix applied! Infinite recursion stopped!' as status;
