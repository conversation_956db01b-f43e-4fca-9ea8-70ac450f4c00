-- 🔍 Check Current Users and Fix Access Issues
-- Run this script in your Supabase SQL Editor

-- STEP 1: Check what users exist in auth.users
SELECT 'Auth Users:' as info;
SELECT id, email, created_at FROM auth.users ORDER BY created_at;

-- STEP 2: Check what users exist in store_users
SELECT 'Store Users:' as info;
SELECT id, user_id, role, created_at FROM store_users WHERE store_id = '550e8400-e29b-41d4-a716-446655440001';

-- STEP 3: Check what users exist in users table (if it exists)
SELECT 'Users Table:' as info;
SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at;

-- STEP 4: Show current policies
SELECT 'Current Policies:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('store_users', 'users')
ORDER BY tablename, policyname;
