import { supabase } from '@/lib/supabase'
import type { 
  Order, 
  OrderFilter, 
  OrderFormData, 
  OrderStats,
  RefundRequest,
  ShippingUpdate,
  Customer
} from '@/types/order'

export class OrderService {
  // Order CRUD operations
  static async getOrders(storeId: string, filters: OrderFilter = {}) {
    let query = supabase
      .from('orders')
      .select(`
        *,
        customer:customers(id, email, first_name, last_name),
        order_items:order_items(
          *,
          product:products(id, name, images)
        )
      `)
      .eq('store_id', storeId)

    // Apply filters
    if (filters.search) {
      query = query.or(`order_number.ilike.%${filters.search}%,customer.email.ilike.%${filters.search}%`)
    }

    if (filters.status) {
      query = query.eq('status', filters.status)
    }

    if (filters.payment_status) {
      query = query.eq('payment_status', filters.payment_status)
    }

    if (filters.fulfillment_status) {
      query = query.eq('fulfillment_status', filters.fulfillment_status)
    }

    if (filters.customer_id) {
      query = query.eq('customer_id', filters.customer_id)
    }

    if (filters.date_from) {
      query = query.gte('created_at', filters.date_from)
    }

    if (filters.date_to) {
      query = query.lte('created_at', filters.date_to)
    }

    if (filters.amount_min !== undefined) {
      query = query.gte('total_amount', filters.amount_min)
    }

    if (filters.amount_max !== undefined) {
      query = query.lte('total_amount', filters.amount_max)
    }

    // Sorting
    const sortBy = filters.sort_by || 'created_at'
    const sortOrder = filters.sort_order || 'desc'
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // Pagination
    const page = filters.page || 1
    const limit = filters.limit || 20
    const from = (page - 1) * limit
    const to = from + limit - 1

    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) throw error

    return {
      data: data as Order[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    }
  }

  static async getOrder(id: string) {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:customers(*),
        order_items:order_items(
          *,
          product:products(id, name, images, sku)
        ),
        order_history:order_history(
          *,
          user:users(id, email, first_name, last_name)
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Order
  }

  static async createOrder(storeId: string, orderData: OrderFormData) {
    // Generate order number
    const orderNumber = await this.generateOrderNumber(storeId)

    const { data, error } = await supabase
      .from('orders')
      .insert({
        ...orderData,
        store_id: storeId,
        order_number: orderNumber,
        total_amount: orderData.subtotal + orderData.tax_amount + orderData.shipping_amount - orderData.discount_amount,
      })
      .select()
      .single()

    if (error) throw error

    // Create order items
    if (orderData.items && orderData.items.length > 0) {
      const orderItems = orderData.items.map(item => ({
        order_id: data.id,
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.price,
        total: item.price * item.quantity,
      }))

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems)

      if (itemsError) throw itemsError
    }

    return data as Order
  }

  static async updateOrder(id: string, orderData: Partial<OrderFormData>) {
    const { data, error } = await supabase
      .from('orders')
      .update({
        ...orderData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Order
  }

  static async updateOrderStatus(id: string, status: Order['status'], userId?: string) {
    const { data, error } = await supabase
      .from('orders')
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    // Add to order history
    await this.addOrderHistory(id, `Order status changed to ${status}`, userId)

    return data as Order
  }

  static async updateShipping(shippingUpdate: ShippingUpdate, userId?: string) {
    const { order_id, tracking_number, tracking_url, carrier } = shippingUpdate

    const { data, error } = await supabase
      .from('orders')
      .update({
        tracking_number,
        tracking_url,
        fulfillment_status: 'shipped',
        shipped_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', order_id)
      .select()
      .single()

    if (error) throw error

    // Add to order history
    const message = `Order shipped${carrier ? ` via ${carrier}` : ''}${tracking_number ? ` - Tracking: ${tracking_number}` : ''}`
    await this.addOrderHistory(order_id, message, userId)

    return data as Order
  }

  static async processRefund(refundRequest: RefundRequest, userId?: string) {
    const { order_id, amount, reason } = refundRequest

    // Update order
    const { data, error } = await supabase
      .from('orders')
      .update({
        payment_status: 'refunded',
        updated_at: new Date().toISOString(),
      })
      .eq('id', order_id)
      .select()
      .single()

    if (error) throw error

    // Add to order history
    await this.addOrderHistory(order_id, `Refund processed: $${amount.toFixed(2)} - ${reason}`, userId)

    return data as Order
  }

  static async deleteOrder(id: string) {
    const { error } = await supabase
      .from('orders')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Customer operations
  static async getCustomers(storeId: string) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as Customer[]
  }

  static async getCustomer(id: string) {
    const { data, error } = await supabase
      .from('customers')
      .select(`
        *,
        orders:orders(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Customer
  }

  // Order history
  static async addOrderHistory(orderId: string, message: string, userId?: string) {
    const { error } = await supabase
      .from('order_history')
      .insert({
        order_id: orderId,
        user_id: userId,
        action: 'status_change',
        message,
      })

    if (error) throw error
  }

  // Analytics and stats
  static async getOrderStats(storeId: string): Promise<OrderStats> {
    const { data, error } = await supabase
      .from('orders')
      .select('status, payment_status, total_amount, created_at')
      .eq('store_id', storeId)

    if (error) throw error

    const orders = data || []
    const today = new Date().toISOString().split('T')[0]
    const todayOrders = orders.filter(o => o.created_at.startsWith(today))

    const stats: OrderStats = {
      total_orders: orders.length,
      pending_orders: orders.filter(o => o.status === 'pending').length,
      processing_orders: orders.filter(o => o.status === 'processing').length,
      shipped_orders: orders.filter(o => o.status === 'shipped').length,
      delivered_orders: orders.filter(o => o.status === 'delivered').length,
      cancelled_orders: orders.filter(o => o.status === 'cancelled').length,
      total_revenue: orders.reduce((sum, o) => sum + o.total_amount, 0),
      average_order_value: orders.length > 0 ? orders.reduce((sum, o) => sum + o.total_amount, 0) / orders.length : 0,
      orders_today: todayOrders.length,
      revenue_today: todayOrders.reduce((sum, o) => sum + o.total_amount, 0),
    }

    return stats
  }

  // Utility functions
  static async generateOrderNumber(storeId: string): Promise<string> {
    const { data, error } = await supabase
      .from('orders')
      .select('order_number')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false })
      .limit(1)

    if (error) throw error

    const lastOrder = data?.[0]
    const lastNumber = lastOrder?.order_number ? parseInt(lastOrder.order_number.replace(/\D/g, '')) : 0
    const nextNumber = lastNumber + 1

    return `WOM-${nextNumber.toString().padStart(6, '0')}`
  }

  static formatCurrency(amount: number, currency = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
}
