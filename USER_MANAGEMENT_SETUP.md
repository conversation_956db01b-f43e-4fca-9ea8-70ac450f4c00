# 👥 User Management System — Rebuild Plan (Single Store Ownership)

This document outlines the **removal of all previous user logic** and the implementation of a secure, invite-only **User Management System** for the **Womanza Admin Panel**, designed specifically for **a single store tied to a real Super Admin (store owner)**.

---

## 👵️ Phase 1: Full Cleanup of Previous User Logic

### 🔸 Remove Codebase Components

* All public registration logic
* Any user role toggling features
* All outdated user authentication logic
* Obsolete dashboard pages for user control

### 🔸 Remove Database Tables

* Drop tables like `users`, `roles`, `user_roles`, `permissions` if they exist
* Remove foreign key constraints referencing those tables

### 🔸 Auth Reset

* Disable any open or public registration flow
* Revoke all test/demo users and sessions
* Reset role assignment logic in Supabase/Auth provider

### 🔸 Remove RLS Policies

* Delete outdated RLS rules related to old user structure

---

## 🚀 Phase 2: New User Management System (From Scratch)

### 🌟 Objective

Establish a robust user control system where:

* Only the **first seeded Super Admin** owns the store
* All users must be created by authorized admins
* No open registration exists
* Store identity is permanently tied to the original Super Admin

---

## 🧱 Database Schema

### `admin_users`

| Column         | Type      | Description                                                    |
| -------------- | --------- | -------------------------------------------------------------- |
| id             | UUID      | Primary key (linked to Supabase UID)                           |
| email          | String    | User email (unique)                                            |
| full\_name     | String    | Full name                                                      |
| password\_hash | String    | Hashed password (managed securely via Supabase Auth)           |
| role           | Enum      | One of: `super_admin`, `admin`, `editor`, `viewer`             |
| invited\_by    | UUID      | ID of inviter (nullable if Super Admin)                        |
| created\_at    | Timestamp | Timestamp when user was created                                |
| store\_id      | UUID      | The ID or slug of the store (linked only to Super Admin owner) |

> `store_id` is **mandatory** and stored with every user to trace ownership and prevent impersonation.

---

## 🔐 Roles & Permissions

| Role        | Created By          | Permissions                                |
| ----------- | ------------------- | ------------------------------------------ |
| Super Admin | System (first user) | Full control — owns store and user base    |
| Admin       | Super Admin         | Full admin, cannot override Super Admin    |
| Editor      | Super/Admin         | Product/category/order management only     |
| Viewer      | Super/Admin         | View-only access to analytics and listings |

> 🔐 The **Super Admin is always tied to a specific store** via `store_id`. No one else can create or impersonate them.

---

## 🔒 Security Measures

* 🚫 No public signup
* ✅ Signup only possible through **Super Admin or Admin user creation**
* ✅ Super Admin identity is **hardcoded and unique**
* ✅ Role cannot be escalated after account creation
* ✅ RLS ensures no unauthorized access

### Example RLS Policy for `admin_users`:

```sql
USING (
  store_id = 'womanza-store-id' AND
  (auth.uid() = id OR role IN ('admin', 'super_admin'))
)
```

---

## 💾 Admin Panel UI Structure

### `/users`

* View list of existing users
* View email, role, status
* Edit/delete (by Admin/Super Admin only)

### `/users/add`

**Add User Form Fields:**

* Email *(required)*
* Full Name *(required)*
* Password *(required)*
* Role *(Dropdown — Admin, Editor, Viewer)*

### `/users/:id/edit`

* Edit user details (not for Super Admin)
* Delete or deactivate user
* Change role (only within scope allowed)

---

## 📧 User Login Flow

* Users are created by Super Admin or Admin using the **Add User Form**.
* Users receive their credentials (email + password) directly.
* They can log in immediately using those credentials.
* There is **no self-signup** or password setup link.

---

## 🔐 Store Identity Lock (Super Admin Binding)

To prevent impersonation or unauthorized ownership:

* First Super Admin is **seeded manually during deployment** or app's first launch
* Their `store_id` becomes the permanent identity of the admin panel
* Any additional users must inherit this `store_id`
* Any access attempt to a different store or unlinked data is denied via RLS

---

## ✅ Summary

This updated user management system ensures:

* Clean removal of old logic
* Strong ownership binding to real store owner (Super Admin)
* Role-based access with credential-based login
* Full Supabase Auth + RLS enforcement
* Secure and simplified admin experience tailored for the Womanza Admin Panel
