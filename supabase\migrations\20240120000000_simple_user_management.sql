-- Simple User Management System for Womanza Admin Panel
-- This migration creates a clean user management system without invitations or activity logs

-- Drop old tables if they exist
DROP TABLE IF EXISTS user_invites CASCADE;
DROP TABLE IF EXISTS user_activity_logs CASCADE;

-- Create users table for storing user details
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table
CREATE POLICY "Store users can view users in their store" ON users
    FOR SELECT USING (
        id IN (
            SELECT su.user_id 
            FROM store_users su 
            WHERE su.store_id IN (
                SELECT store_id 
                FROM store_users 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create policy for inserting users (only super_admin and admin)
CREATE POLICY "Store admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Create updated_at trigger for users
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Update store_users table to ensure it has proper structure
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Create trigger for store_users updated_at
CREATE TRIGGER update_store_users_updated_at 
    BEFORE UPDATE ON store_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to create a new user with store association
CREATE OR REPLACE FUNCTION create_user_with_store(
    p_email TEXT,
    p_first_name TEXT,
    p_last_name TEXT,
    p_password_hash TEXT,
    p_role user_role,
    p_store_id UUID
)
RETURNS JSONB AS $$
DECLARE
    new_user_id UUID;
    result JSONB;
BEGIN
    -- Check if current user can create users
    IF NOT EXISTS (
        SELECT 1 
        FROM store_users 
        WHERE user_id = auth.uid() 
        AND store_id = p_store_id 
        AND role IN ('super_admin', 'admin')
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Permission denied'
        );
    END IF;

    -- Check if email already exists
    IF EXISTS (SELECT 1 FROM users WHERE email = p_email) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Email already exists'
        );
    END IF;

    -- Insert new user
    INSERT INTO users (email, first_name, last_name, password_hash)
    VALUES (p_email, p_first_name, p_last_name, p_password_hash)
    RETURNING id INTO new_user_id;

    -- Create store_users record
    INSERT INTO store_users (store_id, user_id, role)
    VALUES (p_store_id, new_user_id, p_role);

    RETURN jsonb_build_object(
        'success', true,
        'user_id', new_user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_user_with_store(TEXT, TEXT, TEXT, TEXT, user_role, UUID) TO authenticated;

-- Function to get all users for a store
CREATE OR REPLACE FUNCTION get_store_users(p_store_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    role user_role,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Check if current user has access to this store
    IF NOT EXISTS (
        SELECT 1 
        FROM store_users 
        WHERE user_id = auth.uid() 
        AND store_id = p_store_id
    ) THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        su.id,
        su.user_id,
        u.email,
        u.first_name,
        u.last_name,
        su.role,
        su.created_at,
        su.updated_at
    FROM store_users su
    LEFT JOIN users u ON su.user_id = u.id
    WHERE su.store_id = p_store_id
    ORDER BY su.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_store_users(UUID) TO authenticated;
