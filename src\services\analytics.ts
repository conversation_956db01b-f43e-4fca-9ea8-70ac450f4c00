import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'

type Order = Database['public']['Tables']['orders']['Row']
type Customer = Database['public']['Tables']['customers']['Row']
type Product = Database['public']['Tables']['products']['Row']

export interface DashboardStats {
  totalRevenue: number
  revenueChange: number
  totalOrders: number
  ordersChange: number
  totalCustomers: number
  customersChange: number
  conversionRate: number
  conversionChange: number
}

export interface SalesData {
  name: string
  sales: number
  date: string
}

export interface RecentOrder {
  id: string
  customer_name: string
  product_name: string
  total: number
  status: string
  created_at: string
}

export class AnalyticsService {
  static async getDashboardStats(storeId: string, period: 'day' | 'week' | 'month' = 'month'): Promise<DashboardStats> {
    try {
      const now = new Date()
      const periodStart = new Date()
      const previousPeriodStart = new Date()
      
      // Calculate date ranges based on period
      switch (period) {
        case 'day':
          periodStart.setDate(now.getDate() - 1)
          previousPeriodStart.setDate(now.getDate() - 2)
          break
        case 'week':
          periodStart.setDate(now.getDate() - 7)
          previousPeriodStart.setDate(now.getDate() - 14)
          break
        case 'month':
          periodStart.setMonth(now.getMonth() - 1)
          previousPeriodStart.setMonth(now.getMonth() - 2)
          break
      }

      // Get current period data
      const { data: currentOrders } = await supabase
        .from('orders')
        .select('total, created_at, customer_id')
        .eq('store_id', storeId)
        .gte('created_at', periodStart.toISOString())
        .lte('created_at', now.toISOString())

      // Get previous period data for comparison
      const { data: previousOrders } = await supabase
        .from('orders')
        .select('total, created_at, customer_id')
        .eq('store_id', storeId)
        .gte('created_at', previousPeriodStart.toISOString())
        .lt('created_at', periodStart.toISOString())

      // Get total customers
      const { count: totalCustomers } = await supabase
        .from('customers')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', storeId)

      const { count: previousCustomers } = await supabase
        .from('customers')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', storeId)
        .lt('created_at', periodStart.toISOString())

      // Calculate metrics
      const currentRevenue = currentOrders?.reduce((sum, order) => sum + order.total, 0) || 0
      const previousRevenue = previousOrders?.reduce((sum, order) => sum + order.total, 0) || 0
      const revenueChange = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0

      const currentOrderCount = currentOrders?.length || 0
      const previousOrderCount = previousOrders?.length || 0
      const ordersChange = previousOrderCount > 0 ? ((currentOrderCount - previousOrderCount) / previousOrderCount) * 100 : 0

      const customersChange = previousCustomers && previousCustomers > 0 
        ? (((totalCustomers || 0) - previousCustomers) / previousCustomers) * 100 
        : 0

      // Calculate conversion rate (simplified - orders/customers ratio)
      const conversionRate = totalCustomers && totalCustomers > 0 ? (currentOrderCount / totalCustomers) * 100 : 0
      const previousConversionRate = previousCustomers && previousCustomers > 0 ? (previousOrderCount / previousCustomers) * 100 : 0
      const conversionChange = previousConversionRate > 0 ? ((conversionRate - previousConversionRate) / previousConversionRate) * 100 : 0

      return {
        totalRevenue: currentRevenue,
        revenueChange,
        totalOrders: currentOrderCount,
        ordersChange,
        totalCustomers: totalCustomers || 0,
        customersChange,
        conversionRate,
        conversionChange,
      }
    } catch (error) {
      throw error
    }
  }

  static async getSalesData(storeId: string, days: number = 30): Promise<SalesData[]> {
    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - days)

      const { data: orders } = await supabase
        .from('orders')
        .select('total, created_at')
        .eq('store_id', storeId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: true })

      if (!orders) return []

      // Group orders by date
      const salesByDate = new Map<string, number>()
      
      orders.forEach(order => {
        const date = new Date(order.created_at).toISOString().split('T')[0]
        const currentSales = salesByDate.get(date) || 0
        salesByDate.set(date, currentSales + order.total)
      })

      // Fill in missing dates with 0 sales
      const result: SalesData[] = []
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        const dateStr = date.toISOString().split('T')[0]
        const sales = salesByDate.get(dateStr) || 0
        
        result.push({
          name: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          sales,
          date: dateStr,
        })
      }

      return result
    } catch (error) {
      console.error('Error fetching sales data:', error)
      throw error
    }
  }

  static async getRecentOrders(storeId: string, limit: number = 10): Promise<RecentOrder[]> {
    try {
      const { data: orders } = await supabase
        .from('orders')
        .select(`
          id,
          total,
          status,
          created_at,
          customers (
            first_name,
            last_name
          ),
          order_items (
            products (
              name
            )
          )
        `)
        .eq('store_id', storeId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (!orders) return []

      return orders.map(order => ({
        id: `#${order.id.slice(-4)}`,
        customer_name: order.customers 
          ? `${order.customers.first_name} ${order.customers.last_name}`
          : 'Unknown Customer',
        product_name: order.order_items?.[0]?.products?.name || 'Multiple Items',
        total: order.total,
        status: order.status,
        created_at: order.created_at,
      }))
    } catch (error) {
      console.error('Error fetching recent orders:', error)
      throw error
    }
  }

  static subscribeToOrderUpdates(storeId: string, callback: (payload: any) => void) {
    return supabase
      .channel('orders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders',
          filter: `store_id=eq.${storeId}`,
        },
        callback
      )
      .subscribe()
  }

  static subscribeToCustomerUpdates(storeId: string, callback: (payload: any) => void) {
    return supabase
      .channel('customers')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'customers',
          filter: `store_id=eq.${storeId}`,
        },
        callback
      )
      .subscribe()
  }
}
