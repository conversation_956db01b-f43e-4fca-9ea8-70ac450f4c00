import { AdminRole } from '@/services/admin-user-service'

// Define all possible permissions in the system
export type Permission = 
  // User Management
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  
  // Product Management
  | 'products.view'
  | 'products.create'
  | 'products.edit'
  | 'products.delete'
  
  // Category Management
  | 'categories.view'
  | 'categories.create'
  | 'categories.edit'
  | 'categories.delete'
  
  // Order Management
  | 'orders.view'
  | 'orders.create'
  | 'orders.edit'
  | 'orders.delete'
  | 'orders.fulfill'
  
  // Customer Management
  | 'customers.view'
  | 'customers.create'
  | 'customers.edit'
  | 'customers.delete'
  
  // Analytics & Reports
  | 'analytics.view'
  | 'reports.view'
  | 'reports.export'
  
  // Store Settings
  | 'settings.view'
  | 'settings.edit'
  
  // System Administration
  | 'system.backup'
  | 'system.restore'
  | 'system.logs'

// Define permissions for each role
export const ROLE_PERMISSIONS: Record<AdminRole, Permission[]> = {
  super_admin: [
    // Full access to everything
    'users.view', 'users.create', 'users.edit', 'users.delete',
    'products.view', 'products.create', 'products.edit', 'products.delete',
    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.fulfill',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
    'analytics.view', 'reports.view', 'reports.export',
    'settings.view', 'settings.edit',
    'system.backup', 'system.restore', 'system.logs'
  ],
  
  admin: [
    // Full admin access except system administration
    'users.view', 'users.create', 'users.edit', 'users.delete',
    'products.view', 'products.create', 'products.edit', 'products.delete',
    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.fulfill',
    'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
    'analytics.view', 'reports.view', 'reports.export',
    'settings.view', 'settings.edit'
  ],
  
  editor: [
    // Product/category/order management only
    'products.view', 'products.create', 'products.edit', 'products.delete',
    'categories.view', 'categories.create', 'categories.edit', 'categories.delete',
    'orders.view', 'orders.edit', 'orders.fulfill',
    'customers.view', 'customers.edit',
    'analytics.view', 'reports.view'
  ],
  
  viewer: [
    // View-only access
    'products.view',
    'categories.view',
    'orders.view',
    'customers.view',
    'analytics.view',
    'reports.view'
  ]
}

// Navigation items with required permissions
export interface NavItem {
  id: string
  name: string
  path: string
  icon: string
  requiredPermissions: Permission[]
  children?: NavItem[]
}

export const NAVIGATION_ITEMS: NavItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    path: '/dashboard',
    icon: '📊',
    requiredPermissions: ['analytics.view']
  },
  {
    id: 'catalog',
    name: 'Catalog',
    path: '/catalog',
    icon: '📦',
    requiredPermissions: ['products.view'],
    children: [
      {
        id: 'products',
        name: 'All Products',
        path: '/catalog',
        icon: '📦',
        requiredPermissions: ['products.view']
      },
      {
        id: 'add-product',
        name: 'Add Product',
        path: '/catalog/add',
        icon: '➕',
        requiredPermissions: ['products.create']
      },
      {
        id: 'categories',
        name: 'Categories & Tags',
        path: '/catalog/categories',
        icon: '🏷️',
        requiredPermissions: ['categories.view']
      },
      {
        id: 'inventory',
        name: 'Inventory',
        path: '/catalog/inventory',
        icon: '📋',
        requiredPermissions: ['products.view']
      }
    ]
  },
  {
    id: 'orders',
    name: 'Orders',
    path: '/orders',
    icon: '🛒',
    requiredPermissions: ['orders.view']
  },
  {
    id: 'customers',
    name: 'Customers',
    path: '/customers',
    icon: '👥',
    requiredPermissions: ['customers.view']
  },
  {
    id: 'users',
    name: 'Users',
    path: '/users',
    icon: '👤',
    requiredPermissions: ['users.view']
  },
  {
    id: 'analytics',
    name: 'Analytics',
    path: '/analytics',
    icon: '📈',
    requiredPermissions: ['analytics.view']
  },
  {
    id: 'settings',
    name: 'Settings',
    path: '/settings',
    icon: '⚙️',
    requiredPermissions: ['settings.view']
  }
]

/**
 * Check if a role has a specific permission
 */
export function hasPermission(role: AdminRole | null, permission: Permission): boolean {
  if (!role) return false
  return ROLE_PERMISSIONS[role]?.includes(permission) || false
}

/**
 * Check if a role has any of the specified permissions
 */
export function hasAnyPermission(role: AdminRole | null, permissions: Permission[]): boolean {
  if (!role) return false
  return permissions.some(permission => hasPermission(role, permission))
}

/**
 * Check if a role has all of the specified permissions
 */
export function hasAllPermissions(role: AdminRole | null, permissions: Permission[]): boolean {
  if (!role) return false
  return permissions.every(permission => hasPermission(role, permission))
}

/**
 * Get filtered navigation items based on user role
 */
export function getFilteredNavigation(role: AdminRole | null): NavItem[] {
  if (!role) return []
  
  return NAVIGATION_ITEMS.filter(item => {
    // Check if user has required permissions for main item
    const hasMainPermission = hasAnyPermission(role, item.requiredPermissions)
    
    if (!hasMainPermission) return false
    
    // Filter children based on permissions
    if (item.children) {
      item.children = item.children.filter(child => 
        hasAnyPermission(role, child.requiredPermissions)
      )
    }
    
    return true
  })
}

/**
 * Get role display information
 */
export function getRoleInfo(role: AdminRole) {
  const roleInfo = {
    super_admin: {
      name: 'Super Admin',
      description: 'Store Owner - Full control over everything',
      color: 'red',
      badge: '👑'
    },
    admin: {
      name: 'Admin',
      description: 'Store Manager - Full admin access',
      color: 'purple',
      badge: '🛡️'
    },
    editor: {
      name: 'Editor',
      description: 'Content Manager - Product & order management',
      color: 'blue',
      badge: '✏️'
    },
    viewer: {
      name: 'Viewer',
      description: 'Read-only access to data and analytics',
      color: 'green',
      badge: '👁️'
    }
  }
  
  return roleInfo[role]
}

/**
 * Check if user can perform action on target user
 */
export function canManageUser(
  currentRole: AdminRole | null,
  targetRole: AdminRole,
  action: 'view' | 'edit' | 'delete'
): boolean {
  if (!currentRole) return false
  
  // Super admin can do everything except delete themselves
  if (currentRole === 'super_admin') {
    return true
  }
  
  // Admin can manage editor and viewer, but not super_admin
  if (currentRole === 'admin') {
    if (targetRole === 'super_admin') return false
    return true
  }
  
  // Editor and viewer can only view
  return action === 'view'
}

/**
 * Get available roles that current user can assign
 */
export function getAssignableRoles(currentRole: AdminRole | null): AdminRole[] {
  if (!currentRole) return []
  
  if (currentRole === 'super_admin') {
    return ['admin', 'editor', 'viewer']
  }
  
  if (currentRole === 'admin') {
    return ['editor', 'viewer']
  }
  
  return []
}
