import React from 'react'
import { useStore } from '@/contexts/store-context'
import { UserRole } from '@/types/user'

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: string
  role?: UserRole | UserRole[]
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL permissions/roles
}

/**
 * Permission Guard Component
 * 
 * Conditionally renders children based on user permissions or roles.
 * 
 * @param permission - Single permission to check
 * @param role - Single role or array of roles to check
 * @param fallback - Component to render if permission check fails
 * @param requireAll - If true with multiple roles, user must have ALL roles
 */
export function PermissionGuard({
  children,
  permission,
  role,
  fallback = null,
  requireAll = false,
}: PermissionGuardProps) {
  const { hasPermission, getUserRole } = useStore()
  
  const userRole = getUserRole()
  
  // Check permission if provided
  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>
  }
  
  // Check role if provided
  if (role) {
    const roles = Array.isArray(role) ? role : [role]
    
    if (requireAll) {
      // User must have ALL specified roles (unlikely use case, but supported)
      const hasAllRoles = roles.every(r => userRole === r)
      if (!hasAllRoles) {
        return <>{fallback}</>
      }
    } else {
      // User must have at least ONE of the specified roles
      const hasAnyRole = roles.some(r => userRole === r)
      if (!hasAnyRole) {
        return <>{fallback}</>
      }
    }
  }
  
  return <>{children}</>
}

/**
 * Hook for checking permissions in components
 */
export function usePermissions() {
  const { hasPermission, getUserRole, isOwner, isAdmin, canManageUsers } = useStore()
  
  return {
    hasPermission,
    getUserRole,
    isOwner,
    isAdmin,
    canManageUsers,
    
    // Convenience methods for common permission checks
    canViewAnalytics: () => hasPermission('view_analytics'),
    canManageProducts: () => hasPermission('manage_products'),
    canViewProducts: () => hasPermission('view_products'),
    canManageOrders: () => hasPermission('manage_orders'),
    canViewOrders: () => hasPermission('view_orders'),
    canManageCustomers: () => hasPermission('manage_customers'),
    canViewCustomers: () => hasPermission('view_customers'),
    canManageMarketing: () => hasPermission('manage_marketing'),
    canManageSettings: () => hasPermission('manage_store_settings'),
    canInviteUsers: () => hasPermission('invite_users'),
    
    // Role-based checks
    isSuperAdmin: () => getUserRole() === 'super_admin',
    isAdminOrAbove: () => ['super_admin', 'admin'].includes(getUserRole() || ''),
    isEditorOrAbove: () => ['super_admin', 'admin', 'editor'].includes(getUserRole() || ''),
    
    // Combined permission checks
    canFullyManageProducts: () => 
      hasPermission('manage_products') && 
      hasPermission('manage_categories') && 
      hasPermission('manage_inventory'),
      
    canFullyManageOrders: () =>
      hasPermission('manage_orders') && 
      hasPermission('update_order_status'),
      
    canAccessAdminFeatures: () =>
      hasPermission('manage_users') || 
      hasPermission('manage_system_settings'),
  }
}

/**
 * Higher-order component for protecting entire routes
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permission: string,
  fallback?: React.ComponentType
) {
  return function ProtectedComponent(props: P) {
    const { hasPermission } = useStore()
    
    if (!hasPermission(permission)) {
      if (fallback) {
        const FallbackComponent = fallback
        return <FallbackComponent />
      }
      
      return (
        <div className="text-center py-12">
          <div className="h-12 w-12 bg-admin-error rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold">!</span>
          </div>
          <h3 className="text-lg font-medium text-admin-text-primary mb-2">
            Access Denied
          </h3>
          <p className="text-admin-text-muted">
            You don't have permission to access this page.
          </p>
        </div>
      )
    }
    
    return <Component {...props} />
  }
}

/**
 * Component for displaying user role badge
 */
export function UserRoleBadge({ className = '' }: { className?: string }) {
  const { getUserRole } = useStore()
  const role = getUserRole()
  
  if (!role) return null
  
  const roleConfig = {
    super_admin: { label: 'Super Admin', color: 'bg-admin-error text-white' },
    admin: { label: 'Admin', color: 'bg-admin-warning text-white' },
    editor: { label: 'Editor', color: 'bg-admin-info text-white' },
    viewer: { label: 'Viewer', color: 'bg-admin-text-muted text-white' },
  }
  
  const config = roleConfig[role]
  
  return (
    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.color} ${className}`}>
      {config.label}
    </span>
  )
}

/**
 * Component for displaying permission-based content
 */
interface ConditionalRenderProps {
  condition: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ConditionalRender({ condition, children, fallback = null }: ConditionalRenderProps) {
  return condition ? <>{children}</> : <>{fallback}</>
}

/**
 * Permission-aware button component
 */
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permission?: string
  role?: UserRole | UserRole[]
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function PermissionButton({ 
  permission, 
  role, 
  children, 
  fallback = null, 
  ...buttonProps 
}: PermissionButtonProps) {
  return (
    <PermissionGuard permission={permission} role={role} fallback={fallback}>
      <button {...buttonProps}>
        {children}
      </button>
    </PermissionGuard>
  )
}

/**
 * Permission-aware link component
 */
interface PermissionLinkProps {
  permission?: string
  role?: UserRole | UserRole[]
  children: React.ReactNode
  fallback?: React.ReactNode
  to: string
  className?: string
}

export function PermissionLink({ 
  permission, 
  role, 
  children, 
  fallback = null, 
  to,
  className = '',
  ...linkProps 
}: PermissionLinkProps) {
  return (
    <PermissionGuard permission={permission} role={role} fallback={fallback}>
      <a href={to} className={className} {...linkProps}>
        {children}
      </a>
    </PermissionGuard>
  )
}
