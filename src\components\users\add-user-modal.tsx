import React, { useState } from 'react'
import { XMarkIcon, UserIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { UserRole } from '@/types/user'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import toast from 'react-hot-toast'

interface AddUserModalProps {
  isOpen: boolean
  onClose: () => void
  onAddUser: (userData: {
    email: string
    firstName: string
    lastName: string
    password: string
    role: UserRole
  }) => Promise<void>
}

export function AddUserModal({ isOpen, onClose, onAddUser }: AddUserModalProps) {
  const { getUserRole } = useStore()
  const [email, setEmail] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [password, setPassword] = useState('')
  const [role, setRole] = useState<UserRole>('viewer')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  const currentUserRole = getUserRole()

  const availableRoles: {
    value: UserRole;
    label: string;
    description: string;
    permissions: string[];
  }[] = [
    {
      value: 'viewer',
      label: 'Viewer',
      description: 'Read-only access to view data and reports',
      permissions: [
        '👀 View dashboard and analytics',
        '📊 View products and inventory',
        '📋 View orders and customers',
        '📈 View reports and statistics',
        '❌ Cannot create, edit, or delete'
      ]
    },
    {
      value: 'editor',
      label: 'Editor',
      description: 'Can create and edit content, but limited admin access',
      permissions: [
        '✅ All Viewer permissions',
        '➕ Create and edit products',
        '📝 Manage orders and customers',
        '🏷️ Manage categories and tags',
        '📦 Update inventory levels',
        '❌ Cannot manage users or settings'
      ]
    },
    {
      value: 'admin',
      label: 'Admin',
      description: 'Full panel control, except Super Admin areas',
      permissions: [
        '✅ All Editor permissions',
        '👥 Manage users (create, edit, delete)',
        '⚙️ Access system settings',
        '🔧 Configure store settings',
        '📊 Advanced analytics and reports',
        '❌ Cannot delete Super Admin'
      ]
    }
  ]

  // Filter roles based on current user's permissions
  const filteredRoles = availableRoles.filter(roleOption => {
    if (currentUserRole === 'super_admin') {
      return true // Super admin can assign any role
    }
    if (currentUserRole === 'admin') {
      return roleOption.value !== 'admin' // Admin cannot create other admins
    }
    return roleOption.value === 'viewer' // Editor and viewer can only create viewers
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!email.trim()) {
      toast.error('Email is required')
      return
    }

    if (!firstName.trim()) {
      toast.error('First name is required')
      return
    }

    if (!lastName.trim()) {
      toast.error('Last name is required')
      return
    }

    if (!password.trim()) {
      toast.error('Password is required')
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address')
      return
    }



    // Enhanced password validation
    if (password.length < 8) {
      toast.error('Password must be at least 8 characters long')
      return
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      toast.error('Password must contain at least one uppercase letter, one lowercase letter, and one number')
      return
    }

    // Name validation
    if (firstName.trim().length < 2 || lastName.trim().length < 2) {
      toast.error('First name and last name must be at least 2 characters long')
      return
    }

    setLoading(true)
    try {
      await onAddUser({
        email: email.toLowerCase().trim(),
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        password: password,
        role
      })
      
      // Reset form
      setEmail('')
      setFirstName('')
      setLastName('')
      setPassword('')
      setRole('viewer')
      setShowPassword(false)
      onClose()
      toast.success('User created successfully!')
    } catch (err) {
      toast.error('Failed to create user')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setEmail('')
    setFirstName('')
    setLastName('')
    setPassword('')
    setRole('viewer')
    setShowPassword(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <UserIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Add New User
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Create a new user account for the store
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Email Field */}
          <div>
            <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
              required
            />
          </div>

          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                First Name *
              </Label>
              <Input
                id="firstName"
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="John"
                className="mt-1"
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Last Name *
              </Label>
              <Input
                id="lastName"
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Doe"
                className="mt-1"
                required
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <Label htmlFor="password" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Password *
            </Label>
            <div className="relative mt-1">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Minimum 6 characters"
                className="pr-10"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                ) : (
                  <EyeIcon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          {/* Role Selection */}
          <div>
            <Label htmlFor="role" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Role & Permissions *
            </Label>
            <select
              id="role"
              value={role}
              onChange={(e) => setRole(e.target.value as UserRole)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              {filteredRoles.map((roleOption) => (
                <option key={roleOption.value} value={roleOption.value}>
                  {roleOption.label} - {roleOption.description}
                </option>
              ))}
            </select>

            {/* Permissions Preview */}
            <div className="mt-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border">
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                {filteredRoles.find(r => r.value === role)?.label} Permissions:
              </h4>
              <ul className="space-y-1">
                {filteredRoles.find(r => r.value === role)?.permissions.map((permission, index) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{permission}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {loading ? 'Creating...' : 'Create User'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
