-- 🔧 Fix Infinite Recursion in Policies
-- Run this script in your Supabase SQL Editor

-- STEP 1: Drop ALL policies on store_users to stop infinite recursion
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'store_users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON store_users';
    END LOOP;
END $$;

-- STEP 2: Create simple, non-recursive policies for store_users
-- Allow users to see their own store_users record
CREATE POLICY "Users can view own store record" ON store_users
    FOR SELECT USING (user_id = auth.uid());

-- Allow super_admin and admin to manage store_users (but avoid recursion)
CREATE POLICY "Admins can manage store users" ON store_users
    FOR ALL USING (
        -- Check if current user is super_admin or admin in ANY store
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
            LIMIT 1
        )
    );

-- STEP 3: Drop ALL policies on users table
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON users';
    END LOOP;
END $$;

-- STEP 4: Create simple policies for users table
-- Allow all authenticated users to read users (we'll tighten this later)
CREATE POLICY "Allow read for authenticated users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow super_admin and admin to insert users
CREATE POLICY "Allow insert for admins" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
            LIMIT 1
        )
    );

-- Allow super_admin and admin to update users
CREATE POLICY "Allow update for admins" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
            LIMIT 1
        )
    );

-- Success message
SELECT 'Infinite recursion fixed! Policies updated successfully!' as status;
