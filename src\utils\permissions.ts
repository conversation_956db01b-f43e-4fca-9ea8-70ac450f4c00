import { UserRole } from '@/types/user'

// Define permission types
export type Permission = 
  | 'view_dashboard'
  | 'view_products'
  | 'create_products'
  | 'edit_products'
  | 'delete_products'
  | 'view_orders'
  | 'create_orders'
  | 'edit_orders'
  | 'delete_orders'
  | 'view_customers'
  | 'create_customers'
  | 'edit_customers'
  | 'delete_customers'
  | 'view_analytics'
  | 'view_reports'
  | 'manage_inventory'
  | 'manage_categories'
  | 'manage_users'
  | 'manage_settings'
  | 'manage_store_settings'
  | 'view_user_activity'
  | 'delete_users'
  | 'create_users'

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  super_admin: [
    'view_dashboard',
    'view_products',
    'create_products',
    'edit_products',
    'delete_products',
    'view_orders',
    'create_orders',
    'edit_orders',
    'delete_orders',
    'view_customers',
    'create_customers',
    'edit_customers',
    'delete_customers',
    'view_analytics',
    'view_reports',
    'manage_inventory',
    'manage_categories',
    'manage_users',
    'manage_settings',
    'manage_store_settings',
    'view_user_activity',
    'delete_users',
    'create_users'
  ],
  admin: [
    'view_dashboard',
    'view_products',
    'create_products',
    'edit_products',
    'delete_products',
    'view_orders',
    'create_orders',
    'edit_orders',
    'delete_orders',
    'view_customers',
    'create_customers',
    'edit_customers',
    'delete_customers',
    'view_analytics',
    'view_reports',
    'manage_inventory',
    'manage_categories',
    'manage_users',
    'manage_settings',
    'view_user_activity',
    'create_users'
  ],
  editor: [
    'view_dashboard',
    'view_products',
    'create_products',
    'edit_products',
    'view_orders',
    'create_orders',
    'edit_orders',
    'view_customers',
    'create_customers',
    'edit_customers',
    'view_analytics',
    'manage_inventory',
    'manage_categories'
  ],
  viewer: [
    'view_dashboard',
    'view_products',
    'view_orders',
    'view_customers',
    'view_analytics',
    'view_reports'
  ]
}

// Dashboard features configuration
export interface DashboardFeature {
  id: string
  name: string
  description: string
  icon: string
  path: string
  requiredPermissions: Permission[]
  category: 'core' | 'management' | 'analytics' | 'settings'
}

export const DASHBOARD_FEATURES: DashboardFeature[] = [
  // Core Features
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'Overview and key metrics',
    icon: 'ChartBarIcon',
    path: '/dashboard',
    requiredPermissions: ['view_dashboard'],
    category: 'core'
  },
  {
    id: 'products',
    name: 'Products',
    description: 'Manage product catalog',
    icon: 'CubeIcon',
    path: '/products',
    requiredPermissions: ['view_products'],
    category: 'core'
  },
  {
    id: 'orders',
    name: 'Orders',
    description: 'View and manage orders',
    icon: 'ShoppingCartIcon',
    path: '/orders',
    requiredPermissions: ['view_orders'],
    category: 'core'
  },
  {
    id: 'customers',
    name: 'Customers',
    description: 'Customer management',
    icon: 'UsersIcon',
    path: '/customers',
    requiredPermissions: ['view_customers'],
    category: 'core'
  },
  
  // Management Features
  {
    id: 'inventory',
    name: 'Inventory',
    description: 'Stock management',
    icon: 'ArchiveBoxIcon',
    path: '/inventory',
    requiredPermissions: ['manage_inventory'],
    category: 'management'
  },
  {
    id: 'categories',
    name: 'Categories & Tags',
    description: 'Organize products',
    icon: 'TagIcon',
    path: '/categories',
    requiredPermissions: ['manage_categories'],
    category: 'management'
  },
  {
    id: 'users',
    name: 'User Management',
    description: 'Manage team members',
    icon: 'UserGroupIcon',
    path: '/users',
    requiredPermissions: ['manage_users'],
    category: 'management'
  },
  
  // Analytics Features
  {
    id: 'analytics',
    name: 'Analytics',
    description: 'Sales and performance analytics',
    icon: 'ChartPieIcon',
    path: '/analytics',
    requiredPermissions: ['view_analytics'],
    category: 'analytics'
  },
  {
    id: 'reports',
    name: 'Reports',
    description: 'Generate detailed reports',
    icon: 'DocumentChartBarIcon',
    path: '/reports',
    requiredPermissions: ['view_reports'],
    category: 'analytics'
  },
  
  // Settings Features
  {
    id: 'settings',
    name: 'Settings',
    description: 'System configuration',
    icon: 'CogIcon',
    path: '/settings',
    requiredPermissions: ['manage_settings'],
    category: 'settings'
  },
  {
    id: 'store-settings',
    name: 'Store Settings',
    description: 'Store configuration',
    icon: 'BuildingStorefrontIcon',
    path: '/store-settings',
    requiredPermissions: ['manage_store_settings'],
    category: 'settings'
  }
]

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  return rolePermissions.includes(permission)
}

/**
 * Check if a user has all required permissions
 */
export function hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission))
}

/**
 * Check if a user has any of the required permissions
 */
export function hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission))
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(userRole: UserRole): Permission[] {
  return ROLE_PERMISSIONS[userRole] || []
}

/**
 * Get available dashboard features for a user role
 */
export function getAvailableFeatures(userRole: UserRole): DashboardFeature[] {
  return DASHBOARD_FEATURES.filter(feature => 
    hasAllPermissions(userRole, feature.requiredPermissions)
  )
}

/**
 * Check if a user can invite/create users with a specific role
 */
export function canInviteUser(currentRole: UserRole, targetRole: UserRole): boolean {
  // Super admin can create any role
  if (currentRole === 'super_admin') return true
  
  // Admin can create editor and viewer (not admin or super_admin)
  if (currentRole === 'admin') {
    return ['editor', 'viewer'].includes(targetRole)
  }
  
  // Editor and viewer cannot create users
  return false
}

/**
 * Check if a user can delete another user
 */
export function canDeleteUser(currentRole: UserRole, targetRole: UserRole): boolean {
  // Cannot delete super admin
  if (targetRole === 'super_admin') return false
  
  // Super admin can delete anyone except super admin
  if (currentRole === 'super_admin') return true
  
  // Admin can delete editor and viewer
  if (currentRole === 'admin') {
    return ['editor', 'viewer'].includes(targetRole)
  }
  
  // Editor and viewer cannot delete users
  return false
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: UserRole): number {
  const levels = {
    'viewer': 1,
    'editor': 2,
    'admin': 3,
    'super_admin': 4
  }
  return levels[role] || 0
}

/**
 * Check if current user can manage target user
 */
export function canManageUser(currentRole: UserRole, targetRole: UserRole): boolean {
  return getRoleLevel(currentRole) > getRoleLevel(targetRole)
}
