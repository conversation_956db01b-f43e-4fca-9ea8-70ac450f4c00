import { supabase } from '@/lib/supabase'
import { UserRole } from '@/types/user'

// Define StoreUser interface locally
interface StoreUser {
  id: string
  user_id: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  status: string
  created_at: string
  updated_at?: string
}

// Define permission check function locally
function canCreateUser(currentRole: UserRole, targetRole: UserRole): boolean {
  // Super admin can create any role
  if (currentRole === 'super_admin') return true

  // Admin can create editor and viewer (not admin)
  if (currentRole === 'admin') {
    return ['editor', 'viewer'].includes(targetRole)
  }

  // Editor and viewer cannot create users
  return false
}

const WOMANZA_STORE = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  name: 'Womanza Jewelry Store'
}

export interface CreateUserData {
  email: string
  firstName: string
  lastName: string
  password: string
  role: UserRole
}

export interface UserResponse {
  success: boolean
  data?: any
  error?: string
}

export class UserService {
  /**
   * Ensure current super admin exists in store_users table
   */
  static async ensureSuperAdminExists() {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // Check if super admin already exists in store_users
      const { data: existingStoreUser } = await supabase
        .from('store_users')
        .select('*')
        .eq('store_id', WOMANZA_STORE.id)
        .eq('user_id', user.id)
        .single()

      if (!existingStoreUser) {
        // Create store_users record for super admin
        await supabase
          .from('store_users')
          .insert({
            store_id: WOMANZA_STORE.id,
            user_id: user.id,
            role: 'super_admin'
          })

        // Create user record in users table (if table exists)
        try {
          await supabase
            .from('users')
            .insert({
              id: user.id,
              email: user.email || '<EMAIL>',
              first_name: user.user_metadata?.first_name || 'Super',
              last_name: user.user_metadata?.last_name || 'Admin',
              password_hash: 'auth_user' // Special marker for auth users
            })
        } catch (userTableError) {
          console.log('Users table not available, skipping user record creation')
        }
      }
    } catch (error) {
      console.error('Error ensuring super admin exists:', error)
    }
  }

  /**
   * Check role limits for the store
   */
  static async checkRoleLimits(role: UserRole): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const { data: storeUsers, error } = await supabase
        .from('store_users')
        .select('role')
        .eq('store_id', WOMANZA_STORE.id)

      if (error) {
        console.error('Error checking role limits:', error)
        return { allowed: false, reason: 'Error checking role limits' }
      }

      const roleCounts = storeUsers?.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      // Check role limits
      if (role === 'super_admin' && (roleCounts['super_admin'] || 0) >= 1) {
        return { allowed: false, reason: 'Only one Super Admin allowed per store' }
      }

      if (role === 'admin' && (roleCounts['admin'] || 0) >= 1) {
        return { allowed: false, reason: 'Only one Admin allowed per store' }
      }

      // Editor and viewer can have multiple users
      return { allowed: true }
    } catch (error) {
      console.error('Error checking role limits:', error)
      return { allowed: false, reason: 'Error checking role limits' }
    }
  }

  /**
   * Get all users for the store
   */
  static async getAllUsers(): Promise<StoreUser[]> {
    try {
      // Get store users with a simple join
      const { data: storeUsers, error } = await supabase
        .from('store_users')
        .select(`
          id,
          user_id,
          role,
          created_at,
          updated_at
        `)
        .eq('store_id', WOMANZA_STORE.id)
        .order('created_at', { ascending: true }) // Changed to ascending for older to newest

      if (error) {
        console.error('Error fetching store users:', error)
        return []
      }

      if (!storeUsers || storeUsers.length === 0) {
        return []
      }

      // Try to get user details from users table
      let users: any[] = []
      try {
        const userIds = storeUsers.map(su => su.user_id)
        const { data: usersData } = await supabase
          .from('users')
          .select('id, email, first_name, last_name')
          .in('id', userIds)
        users = usersData || []
      } catch (error) {
        console.log('Users table not available, using fallback storage')
        // Try to get user details from localStorage
        const userDetails = JSON.parse(localStorage.getItem('user_details') || '{}')
        users = Object.values(userDetails)
      }

      // Get current auth user for super admin details
      const { data: { user: currentUser } } = await supabase.auth.getUser()

      // Combine the data
      const combinedUsers = storeUsers.map((storeUser: any) => {
        const userDetails = users?.find(u => u.id === storeUser.user_id)

        // If this is the current auth user and no user details found, use auth data
        if (!userDetails && currentUser && storeUser.user_id === currentUser.id) {
          return {
            id: storeUser.id,
            user_id: storeUser.user_id,
            email: currentUser.email || '<EMAIL>',
            first_name: currentUser.user_metadata?.first_name || 'Super',
            last_name: currentUser.user_metadata?.last_name || 'Admin',
            role: storeUser.role as UserRole,
            status: 'active',
            created_at: storeUser.created_at,
            updated_at: storeUser.updated_at
          }
        }

        return {
          id: storeUser.id,
          user_id: storeUser.user_id,
          email: userDetails?.email || 'Unknown',
          first_name: userDetails?.first_name || 'Unknown',
          last_name: userDetails?.last_name || 'User',
          role: storeUser.role as UserRole,
          status: 'active',
          created_at: storeUser.created_at,
          updated_at: storeUser.updated_at
        }
      })

      // Sort by role hierarchy: Super Admin, Admin, Editor, Viewer
      // Within same role, sort by creation date (older to newest)
      const roleOrder = { 'super_admin': 0, 'admin': 1, 'editor': 2, 'viewer': 3 }

      return combinedUsers.sort((a, b) => {
        // First sort by role hierarchy
        const roleComparison = roleOrder[a.role] - roleOrder[b.role]
        if (roleComparison !== 0) return roleComparison

        // If same role, sort by creation date (older first)
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      })
    } catch (error) {
      console.error('Error fetching users:', error)
      return []
    }
  }

  /**
   * Create a new user using database function
   */
  static async createUser(
    userData: CreateUserData,
    currentUserRole: UserRole
  ): Promise<UserResponse> {
    try {
      // Check if current user can create this role
      if (!canCreateUser(currentUserRole, userData.role)) {
        return {
          success: false,
          error: 'You do not have permission to create users with this role'
        }
      }

      // Check role limits before creating
      const roleCheckResult = await this.checkRoleLimits(userData.role)
      if (!roleCheckResult.allowed) {
        return {
          success: false,
          error: roleCheckResult.reason || 'Role limit exceeded'
        }
      }

      // Validate input
      if (!userData.email || !userData.firstName || !userData.lastName || !userData.password || !userData.role) {
        return {
          success: false,
          error: 'All fields are required'
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        }
      }

      // Validate password strength
      if (userData.password.length < 6) {
        return {
          success: false,
          error: 'Password must be at least 6 characters long'
        }
      }

      // Simple password hash (in production, use proper bcrypt)
      const passwordHash = btoa(userData.password) // Base64 encoding for demo

      // Generate new user ID
      const newUserId = crypto.randomUUID()

      // Try to create user in users table (if it exists)
      try {
        // Check if email already exists (use limit instead of single to avoid 406 error)
        const { data: existingUsers } = await supabase
          .from('users')
          .select('email')
          .eq('email', userData.email)
          .limit(1)

        if (existingUsers && existingUsers.length > 0) {
          return {
            success: false,
            error: 'Email already exists'
          }
        }

        // Create user in users table
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: newUserId,
            email: userData.email,
            first_name: userData.firstName,
            last_name: userData.lastName,
            password_hash: passwordHash
          })

        if (userError) {
          console.error('Error creating user in users table:', userError)
          throw userError
        }
      } catch (error) {
        console.log('Users table not available, using fallback storage')

        // Store user details in localStorage as fallback
        const existingUsers = JSON.parse(localStorage.getItem('user_details') || '{}')

        // Check if email already exists in localStorage
        const emailExists = Object.values(existingUsers).some((user: any) => user.email === userData.email)
        if (emailExists) {
          return {
            success: false,
            error: 'Email already exists'
          }
        }

        existingUsers[newUserId] = {
          id: newUserId,
          email: userData.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          password_hash: passwordHash,
          created_at: new Date().toISOString()
        }
        localStorage.setItem('user_details', JSON.stringify(existingUsers))
      }

      // Create store_users record
      const { error: storeUserError } = await supabase
        .from('store_users')
        .insert({
          store_id: WOMANZA_STORE.id,
          user_id: newUserId,
          role: userData.role
        })

      if (storeUserError) {
        console.error('Error creating store user:', storeUserError)
        // Clean up user record
        await supabase.from('users').delete().eq('id', newUserId)
        return {
          success: false,
          error: 'Failed to create store user'
        }
      }

      return {
        success: true,
        data: {
          id: newUserId,
          email: userData.email,
          role: userData.role,
          status: 'active',
          created_at: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('Error creating user:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create user'
      }
    }
  }

  /**
   * Update user role
   */
  static async updateUserRole(
    userId: string,
    newRole: UserRole,
    currentUserRole: UserRole
  ): Promise<UserResponse> {
    try {
      // Check if current user can update this role
      if (!canCreateUser(currentUserRole, newRole)) {
        return {
          success: false,
          error: 'You do not have permission to assign this role'
        }
      }

      // Update the role in store_users table
      const { error } = await supabase
        .from('store_users')
        .update({ role: newRole, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('store_id', WOMANZA_STORE.id)

      if (error) {
        console.error('Error updating user role:', error)
        return {
          success: false,
          error: 'Failed to update user role'
        }
      }

      return {
        success: true,
        data: { message: 'User role updated successfully' }
      }
    } catch (error) {
      console.error('Error updating user role:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user role'
      }
    }
  }

  /**
   * Deactivate a user (delete them)
   */
  static async deactivateUser(
    userId: string,
    currentUserRole: UserRole
  ): Promise<UserResponse> {
    try {
      // Only super admin and admin can delete users
      if (!['super_admin', 'admin'].includes(currentUserRole)) {
        return {
          success: false,
          error: 'You do not have permission to delete users'
        }
      }

      // Delete from store_users table
      const { error: storeUserError } = await supabase
        .from('store_users')
        .delete()
        .eq('user_id', userId)
        .eq('store_id', WOMANZA_STORE.id)

      if (storeUserError) {
        console.error('Error deleting store user:', storeUserError)
        return {
          success: false,
          error: 'Failed to delete user'
        }
      }

      // Delete from users table
      const { error: userError } = await supabase
        .from('users')
        .delete()
        .eq('id', userId)

      if (userError) {
        console.error('Error deleting user:', userError)
        // Don't return error here as store_users is already deleted
      }

      return {
        success: true,
        data: { message: 'User deleted successfully' }
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete user'
      }
    }
  }

  /**
   * Check if current user can delete target user
   */
  static canDeleteTargetUser(
    currentUserRole: UserRole,
    targetUserRole: UserRole,
    isSelf: boolean
  ): { allowed: boolean; reason?: string } {
    // Super admin cannot delete themselves
    if (isSelf && currentUserRole === 'super_admin') {
      return { allowed: false, reason: 'Super admin cannot delete themselves' }
    }

    // Super admin can delete anyone else
    if (currentUserRole === 'super_admin') {
      return { allowed: true }
    }

    // Admin can delete editor and viewer (not other admins or super admins)
    if (currentUserRole === 'admin') {
      if (['editor', 'viewer'].includes(targetUserRole)) {
        return { allowed: true }
      }
      return { allowed: false, reason: 'Admin cannot delete other admins or super admins' }
    }

    // Editor and viewer cannot delete anyone
    return { allowed: false, reason: 'You do not have permission to delete users' }
  }
}

export type { StoreUser }
