// Simple email sending API endpoint
// This is a mock implementation - replace with your actual email service

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { to_email, role, invite_url, store_name, expires_date } = req.body

    // Mock email sending - replace with actual email service
    console.log('Sending invitation email:', {
      to: to_email,
      role,
      invite_url,
      store_name,
      expires_date
    })

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // For development, log the email content
    const emailContent = `
Subject: You're invited to join ${store_name}

Hi there!

You've been invited to join ${store_name} as a ${role}.

Click the link below to accept your invitation:
${invite_url}

This invitation expires on ${expires_date}.

Best regards,
The ${store_name} Team
`

    console.log('Email content:', emailContent)

    // Return success
    res.status(200).json({ 
      success: true, 
      message: 'Email sent successfully',
      debug: {
        to: to_email,
        subject: `You're invited to join ${store_name}`,
        preview: emailContent.substring(0, 100) + '...'
      }
    })

  } catch (error) {
    console.error('Error sending email:', error)
    res.status(500).json({ 
      error: 'Failed to send email',
      details: error.message 
    })
  }
}
