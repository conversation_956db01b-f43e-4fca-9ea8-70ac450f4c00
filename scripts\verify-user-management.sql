-- 🔍 User Management System Verification Script
-- Run this after the main migration to verify everything is working

-- STEP 1: Verify table structure
SELECT 'Checking admin_users table structure...' as status;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'admin_users' 
ORDER BY ordinal_position;

-- STEP 2: Check RLS policies
SELECT 'Checking RLS policies...' as status;

SELECT 
    policyname,
    cmd,
    permissive,
    roles,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'admin_users';

-- STEP 3: Verify super admin exists
SELECT 'Checking super admin account...' as status;

SELECT 
    id,
    email,
    full_name,
    role,
    store_id,
    created_at
FROM admin_users 
WHERE role = 'super_admin';

-- STEP 4: Test user creation function
SELECT 'Testing user creation function...' as status;

SELECT create_admin_user(
    '<EMAIL>',
    'Test User',
    'password123',
    'editor',
    '********-0000-0000-0000-************'::uuid
) as test_result;

-- STEP 5: Verify test user was created
SELECT 'Verifying test user creation...' as status;

SELECT 
    email,
    full_name,
    role,
    invited_by
FROM admin_users 
WHERE email = '<EMAIL>';

-- STEP 6: Clean up test user
DELETE FROM admin_users WHERE email = '<EMAIL>';

-- STEP 7: Verify role hierarchy function
SELECT 'Testing role hierarchy function...' as status;

SELECT get_user_role('********-0000-0000-0000-************'::uuid) as super_admin_role;

-- STEP 8: Check storage bucket for product images (if needed)
SELECT 'Checking storage buckets...' as status;

SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'product-images';

-- STEP 9: Final summary
SELECT 'User Management System Verification Complete!' as status;

SELECT 
    'Total admin users: ' || COUNT(*) as summary
FROM admin_users;

SELECT 
    'Super admins: ' || COUNT(*) as super_admin_count
FROM admin_users 
WHERE role = 'super_admin';

SELECT 
    'Admins: ' || COUNT(*) as admin_count
FROM admin_users 
WHERE role = 'admin';

SELECT 
    'Editors: ' || COUNT(*) as editor_count
FROM admin_users 
WHERE role = 'editor';

SELECT 
    'Viewers: ' || COUNT(*) as viewer_count
FROM admin_users 
WHERE role = 'viewer';

-- Success message
SELECT '✅ All systems operational!' as final_status;
