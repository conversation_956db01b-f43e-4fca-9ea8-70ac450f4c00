export interface Order {
  id: string
  store_id: string
  customer_id: string
  order_number: string
  status: OrderStatus
  total_amount: number
  subtotal: number
  tax_amount: number
  shipping_amount: number
  discount_amount: number
  currency: string
  
  // Addresses
  shipping_address: Address
  billing_address?: Address
  
  // Payment
  payment_status: PaymentStatus
  payment_method?: string
  payment_intent_id?: string
  stripe_session_id?: string
  
  // Fulfillment
  fulfillment_status: FulfillmentStatus
  tracking_number?: string
  tracking_url?: string
  shipped_at?: string
  delivered_at?: string
  
  // Metadata
  notes?: string
  tags?: string[]
  source?: string
  
  // Timestamps
  created_at: string
  updated_at: string
  
  // Relations
  customer?: Customer
  order_items?: OrderItem[]
  order_history?: OrderHistory[]
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  variant_id?: string
  quantity: number
  price: number
  total: number
  sku?: string
  title: string
  variant_title?: string
  image?: string
  
  // Product details at time of order
  product_snapshot?: {
    name: string
    description?: string
    images: string[]
    metal_type?: string
    gemstone_type?: string
  }
  
  created_at: string
  updated_at: string
  
  // Relations
  product?: Product
}

export interface Customer {
  id: string
  store_id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  accepts_marketing: boolean
  total_spent: number
  orders_count: number
  
  // Default addresses
  default_address?: Address
  addresses?: Address[]
  
  // Metadata
  tags?: string[]
  notes?: string
  
  created_at: string
  updated_at: string
  last_order_at?: string
}

export interface Address {
  id?: string
  first_name: string
  last_name: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  zip: string
  phone?: string
}

export interface OrderHistory {
  id: string
  order_id: string
  user_id?: string
  action: string
  message: string
  created_at: string
  
  // Relations
  user?: {
    id: string
    email: string
    first_name?: string
    last_name?: string
  }
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded'
  | 'returned'

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'partially_paid'
  | 'refunded'
  | 'partially_refunded'
  | 'failed'
  | 'cancelled'

export type FulfillmentStatus = 
  | 'unfulfilled'
  | 'partial'
  | 'fulfilled'
  | 'shipped'
  | 'delivered'
  | 'cancelled'

export interface OrderFilter {
  search?: string
  status?: OrderStatus
  payment_status?: PaymentStatus
  fulfillment_status?: FulfillmentStatus
  customer_id?: string
  date_from?: string
  date_to?: string
  amount_min?: number
  amount_max?: number
  sort_by?: 'created_at' | 'updated_at' | 'total_amount' | 'order_number'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface OrderStats {
  total_orders: number
  pending_orders: number
  processing_orders: number
  shipped_orders: number
  delivered_orders: number
  cancelled_orders: number
  total_revenue: number
  average_order_value: number
  orders_today: number
  revenue_today: number
}

export interface OrderFormData {
  customer_id: string
  status: OrderStatus
  payment_status: PaymentStatus
  fulfillment_status: FulfillmentStatus
  
  // Items
  items: {
    product_id: string
    quantity: number
    price: number
  }[]
  
  // Addresses
  shipping_address: Address
  billing_address?: Address
  
  // Amounts
  subtotal: number
  tax_amount: number
  shipping_amount: number
  discount_amount: number
  
  // Payment
  payment_method?: string
  
  // Metadata
  notes?: string
  tags?: string[]
}

export interface RefundRequest {
  order_id: string
  amount: number
  reason: string
  items?: {
    order_item_id: string
    quantity: number
    amount: number
  }[]
  notify_customer: boolean
}

export interface ShippingUpdate {
  order_id: string
  tracking_number?: string
  tracking_url?: string
  carrier?: string
  notify_customer: boolean
}

// Order status configurations
export const ORDER_STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800',
    description: 'Order received, awaiting confirmation'
  },
  confirmed: {
    label: 'Confirmed',
    color: 'bg-blue-100 text-blue-800',
    description: 'Order confirmed, preparing for processing'
  },
  processing: {
    label: 'Processing',
    color: 'bg-purple-100 text-purple-800',
    description: 'Order is being prepared'
  },
  shipped: {
    label: 'Shipped',
    color: 'bg-indigo-100 text-indigo-800',
    description: 'Order has been shipped'
  },
  delivered: {
    label: 'Delivered',
    color: 'bg-green-100 text-green-800',
    description: 'Order has been delivered'
  },
  cancelled: {
    label: 'Cancelled',
    color: 'bg-red-100 text-red-800',
    description: 'Order has been cancelled'
  },
  refunded: {
    label: 'Refunded',
    color: 'bg-gray-100 text-gray-800',
    description: 'Order has been refunded'
  },
  returned: {
    label: 'Returned',
    color: 'bg-orange-100 text-orange-800',
    description: 'Order has been returned'
  }
} as const

export const PAYMENT_STATUS_CONFIG = {
  pending: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800'
  },
  paid: {
    label: 'Paid',
    color: 'bg-green-100 text-green-800'
  },
  partially_paid: {
    label: 'Partially Paid',
    color: 'bg-blue-100 text-blue-800'
  },
  refunded: {
    label: 'Refunded',
    color: 'bg-gray-100 text-gray-800'
  },
  partially_refunded: {
    label: 'Partially Refunded',
    color: 'bg-orange-100 text-orange-800'
  },
  failed: {
    label: 'Failed',
    color: 'bg-red-100 text-red-800'
  },
  cancelled: {
    label: 'Cancelled',
    color: 'bg-red-100 text-red-800'
  }
} as const

export const FULFILLMENT_STATUS_CONFIG = {
  unfulfilled: {
    label: 'Unfulfilled',
    color: 'bg-gray-100 text-gray-800'
  },
  partial: {
    label: 'Partially Fulfilled',
    color: 'bg-yellow-100 text-yellow-800'
  },
  fulfilled: {
    label: 'Fulfilled',
    color: 'bg-blue-100 text-blue-800'
  },
  shipped: {
    label: 'Shipped',
    color: 'bg-indigo-100 text-indigo-800'
  },
  delivered: {
    label: 'Delivered',
    color: 'bg-green-100 text-green-800'
  },
  cancelled: {
    label: 'Cancelled',
    color: 'bg-red-100 text-red-800'
  }
} as const
