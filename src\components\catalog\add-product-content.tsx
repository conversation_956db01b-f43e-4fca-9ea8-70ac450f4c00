import React, { useState, useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import {
  PhotoIcon,
  PlusIcon,
  XMarkIcon,
  TagIcon,
  CurrencyDollarIcon,
  CubeIcon,
  EyeIcon,
  CheckCircleIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline'
import { ProductService } from '@/services/product-service'
import { useStore } from '@/contexts/store-context'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import { AddProductTabs } from './add-product-tabs'

interface ProductForm {
  name: string
  description: string
  short_description: string
  sku: string
  price: number
  compare_at_price: number
  cost_price: number
  inventory_quantity: number
  low_stock_threshold: number
  track_inventory: boolean
  status: 'active' | 'draft' | 'archived'
  is_featured: boolean
  weight_grams: number
  category_id: string
  images: string[]
  seo_title: string
  seo_description: string
  // Jewelry specific fields
  metal_type: string
  gemstone_type: string
  gemstone_carat: number
  metal_purity: string
}

interface Category {
  id: string
  name: string
}

export function AddProductContent() {
  const navigate = useNavigate()
  const { currentStore } = useStore()
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [activeTab, setActiveTab] = useState('basic')
  const [uploadingImages, setUploadingImages] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)
  
  const [form, setForm] = useState<ProductForm>({
    name: '',
    description: '',
    short_description: '',
    sku: '',
    price: 0,
    compare_at_price: 0,
    cost_price: 0,
    inventory_quantity: 0,
    low_stock_threshold: 5,
    track_inventory: true,
    status: 'draft',
    is_featured: false,
    weight_grams: 0,
    category_id: '',
    images: [],
    seo_title: '',
    seo_description: '',
    // Jewelry specific fields
    metal_type: '',
    gemstone_type: '',
    gemstone_carat: 0,
    metal_purity: ''
  })

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const data = await ProductService.getCategories(currentStore.id)
      setCategories(data || [])
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const handleInputChange = (field: keyof ProductForm, value: any) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Image upload functionality
  const uploadImageToSupabase = async (file: File): Promise<string> => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `products/${fileName}`

    const { data, error } = await supabase.storage
      .from('product-images')
      .upload(filePath, file)

    if (error) {
      console.error('Upload error:', error)
      throw new Error('Failed to upload image')
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(filePath)

    return publicUrl
  }

  const handleImageUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    try {
      setUploadingImages(true)
      const uploadPromises = Array.from(files).map(file => uploadImageToSupabase(file))
      const uploadedUrls = await Promise.all(uploadPromises)
      
      // Add new images to existing ones
      const newImages = [...form.images, ...uploadedUrls]
      handleInputChange('images', newImages)
      
      toast.success(`${uploadedUrls.length} image(s) uploaded successfully!`)
    } catch (error) {
      console.error('Error uploading images:', error)
      toast.error('Failed to upload images. Please try again.')
    } finally {
      setUploadingImages(false)
    }
  }

  const removeImage = (index: number) => {
    const newImages = form.images.filter((_, i) => i !== index)
    handleInputChange('images', newImages)
  }

  const generateSKU = () => {
    const prefix = 'WOM'
    const timestamp = Date.now().toString().slice(-6)
    const random = Math.random().toString(36).substring(2, 5).toUpperCase()
    return `${prefix}-${timestamp}-${random}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!form.name.trim()) {
      toast.error('Product name is required')
      return
    }

    if (form.price <= 0) {
      toast.error('Product price must be greater than 0')
      return
    }

    try {
      setLoading(true)
      
      // Generate SKU if not provided
      if (!form.sku.trim()) {
        form.sku = generateSKU()
      }

      // Generate slug from name
      const slug = form.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')

      // Prepare product data for database
      const productData = {
        name: form.name,
        slug: slug,
        description: form.description,
        short_description: form.short_description,
        sku: form.sku,
        price: form.price,
        compare_at_price: form.compare_at_price || null,
        cost_price: form.cost_price || null,
        track_inventory: form.track_inventory,
        inventory_quantity: form.inventory_quantity,
        low_stock_threshold: form.low_stock_threshold,
        status: form.status,
        is_featured: form.is_featured,
        weight_grams: form.weight_grams || null,
        category_id: form.category_id || null,
        images: form.images,
        meta_title: form.seo_title,
        meta_description: form.seo_description,
        // Jewelry specific fields
        metal_type: form.metal_type,
        gemstone_type: form.gemstone_type,
        gemstone_carat: form.gemstone_carat || null,
        metal_purity: form.metal_purity,
        store_id: currentStore.id
      }

      // Save to database
      const { data, error } = await supabase
        .from('products')
        .insert(productData)
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        throw new Error(error.message)
      }

      toast.success('✅ Product created successfully and is now live!')
      navigate({ to: '/catalog' })
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Failed to create product. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveAsDraft = () => {
    handleInputChange('status', 'draft')
    setTimeout(() => {
      const form = document.getElementById('product-form') as HTMLFormElement
      form?.requestSubmit()
    }, 100)
  }

  const handlePublish = () => {
    handleInputChange('status', 'active')
    setTimeout(() => {
      const form = document.getElementById('product-form') as HTMLFormElement
      form?.requestSubmit()
    }, 100)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add New Product</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create a new product for your jewelry store
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={handleSaveAsDraft}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
          >
            Save as Draft
          </button>
          <button
            type="button"
            onClick={handlePublish}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
          >
            {loading ? 'Publishing...' : 'Publish Product'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'basic', name: 'Basic Info', icon: '📝' },
            { id: 'pricing', name: 'Pricing', icon: '💰' },
            { id: 'inventory', name: 'Inventory', icon: '📦' },
            { id: 'jewelry', name: 'Jewelry Details', icon: '💎' },
            { id: 'images', name: 'Images', icon: '🖼️' },
            { id: 'seo', name: 'SEO', icon: '🔍' },
            { id: 'preview', name: 'Preview', icon: '👁️' }
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      <form id="product-form" onSubmit={handleSubmit} className="space-y-8">
        {/* Tab Content */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <AddProductTabs
            activeTab={activeTab}
            form={form}
            categories={categories}
            uploadingImages={uploadingImages}
            handleInputChange={handleInputChange}
            handleImageUpload={handleImageUpload}
            removeImage={removeImage}
            generateSKU={generateSKU}
          />
        </div>
      </form>
    </div>
  )
}
