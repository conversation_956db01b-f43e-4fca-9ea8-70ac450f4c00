import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './auth-context'
import { supabase, Database } from '@/lib/supabase'
import { env } from '@/lib/env'
import { WOMANZA_STORE } from '@/lib/constants'
import { AdminRole } from '@/services/admin-user-service'
import { AdminUserService } from '@/services/admin-user-service'
import { hasPermission as checkPermission } from '@/utils/permissions'

interface StoreContextType {
  currentStore: typeof WOMANZA_STORE
  userRole: AdminRole | null
  loading: boolean
  error: string | null
  refreshUser: () => Promise<void>
  hasPermission: (permission: string) => boolean
  getUserRole: () => AdminRole | null
  isOwner: () => boolean
  isAdmin: () => boolean
  canManageUsers: () => boolean
}

const StoreContext = createContext<StoreContextType | undefined>(undefined)

export function useStore() {
  const context = useContext(StoreContext)
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider')
  }
  return context
}

interface StoreProviderProps {
  children: React.ReactNode
}

export function StoreProvider({ children }: StoreProviderProps) {
  const { user } = useAuth()
  const [userRole, setUserRole] = useState<AdminRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load user's role when user changes
  useEffect(() => {
    if (user) {
      loadUserRole()
    } else {
      setUserRole(null)
      setLoading(false)
    }
  }, [user])

  const loadUserRole = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      // Get user's role from admin_users table
      const role = await AdminUserService.getCurrentUserRole()

      if (!role) {
        // Check if this is the super admin email
        if (user.email === '<EMAIL>') {
          // Try to create/update super admin record
          try {
            const { error: upsertError } = await supabase
              .from('admin_users')
              .upsert({
                id: user.id,
                email: user.email,
                full_name: 'Womanza Store Owner',
                password_hash: 'supabase_auth_managed',
                role: 'super_admin',
                invited_by: null,
                store_id: '550e8400-e29b-41d4-a716-446655440001'
              })

            if (!upsertError) {
              setUserRole('super_admin')
              setLoading(false)
              return
            }
          } catch (insertErr) {
            console.error('Error creating super admin record:', insertErr)
          }
        }

        setError(`No access found for user ${user.email}. Please contact an administrator to get access to the Womanza Admin Panel.`)
        return
      }

      setUserRole(role)
    } catch (error) {
      console.error('Error in loadUserRole:', error)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    await loadUserRole()
  }

  const hasPermission = (permission: string): boolean => {
    if (!userRole || !user) return false
    return checkPermission(userRole, permission as any)
  }

  const getUserRole = (): AdminRole | null => {
    return userRole
  }

  const isOwner = (): boolean => {
    return userRole === 'super_admin'
  }

  const isAdmin = (): boolean => {
    return userRole === 'super_admin' || userRole === 'admin'
  }

  const canManageUsers = (): boolean => {
    return userRole === 'super_admin' || userRole === 'admin'
  }

  const value: StoreContextType = {
    currentStore: WOMANZA_STORE,
    userRole,
    loading,
    error,
    refreshUser,
    hasPermission,
    getUserRole,
    isOwner,
    isAdmin,
    canManageUsers,
  }

  return <StoreContext.Provider value={value}>{children}</StoreContext.Provider>
}
