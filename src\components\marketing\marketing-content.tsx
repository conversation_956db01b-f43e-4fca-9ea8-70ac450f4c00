import React, { useState } from 'react'
import {
  MegaphoneIcon,
  TagIcon,
  EnvelopeIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  CalendarIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'
import toast from 'react-hot-toast'

interface Promotion {
  id: string
  name: string
  type: 'discount' | 'bogo' | 'free_shipping'
  value: number
  code: string
  status: 'active' | 'scheduled' | 'expired'
  start_date: string
  end_date: string
  usage_count: number
  usage_limit?: number
}

interface Campaign {
  id: string
  name: string
  subject: string
  status: 'draft' | 'scheduled' | 'sent'
  recipients: number
  open_rate?: number
  click_rate?: number
  scheduled_at?: string
  sent_at?: string
}

export function MarketingContent() {
  const { hasPermission } = useStore()
  const [activeTab, setActiveTab] = useState<'promotions' | 'campaigns'>('promotions')

  // TODO: Implement real promotions and campaigns fetching from Supabase
  const promotions: Promotion[] = []
  const campaigns: Campaign[] = []

  const getPromotionTypeLabel = (type: string) => {
    switch (type) {
      case 'discount':
        return 'Percentage Off'
      case 'bogo':
        return 'Buy One Get One'
      case 'free_shipping':
        return 'Free Shipping'
      default:
        return type
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'sent':
        return 'bg-admin-success/10 text-admin-success'
      case 'scheduled':
        return 'bg-admin-info/10 text-admin-info'
      case 'expired':
      case 'draft':
        return 'bg-admin-text-muted/10 text-admin-text-muted'
      default:
        return 'bg-admin-text-muted/10 text-admin-text-muted'
    }
  }

  if (!hasPermission('manage_marketing')) {
    return (
      <div className="text-center py-12">
        <MegaphoneIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
        <h3 className="text-lg font-medium text-admin-text-primary mb-2">
          Access Denied
        </h3>
        <p className="text-admin-text-muted">
          You don't have permission to access marketing tools.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-admin-text-primary">
            Marketing
          </h1>
          <p className="text-admin-text-muted mt-1">
            Manage promotions and email campaigns to drive sales
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="ghost" className="flex items-center gap-2">
            <ChartBarIcon className="h-4 w-4" />
            Analytics
          </Button>
          <Button className="flex items-center gap-2">
            <PlusIcon className="h-4 w-4" />
            {activeTab === 'promotions' ? 'New Promotion' : 'New Campaign'}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TagIcon className="h-8 w-8 text-admin-primary" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Active Promotions</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {promotions.filter(p => p.status === 'active').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <EnvelopeIcon className="h-8 w-8 text-admin-success" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Campaigns Sent</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {campaigns.filter(c => c.status === 'sent').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserGroupIcon className="h-8 w-8 text-admin-info" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Total Reach</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {campaigns.reduce((acc, c) => acc + (c.status === 'sent' ? c.recipients : 0), 0)}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-admin-warning" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Avg. Open Rate</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {(campaigns.filter(c => c.open_rate).reduce((acc, c) => acc + (c.open_rate || 0), 0) / 
                  Math.max(campaigns.filter(c => c.open_rate).length, 1)).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-admin-border">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('promotions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
              activeTab === 'promotions'
                ? 'border-admin-primary text-admin-primary'
                : 'border-transparent text-admin-text-muted hover:text-admin-text-secondary hover:border-admin-border'
            }`}
          >
            <TagIcon className="h-4 w-4" />
            Promotions ({promotions.length})
          </button>
          <button
            onClick={() => setActiveTab('campaigns')}
            className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
              activeTab === 'campaigns'
                ? 'border-admin-primary text-admin-primary'
                : 'border-transparent text-admin-text-muted hover:text-admin-text-secondary hover:border-admin-border'
            }`}
          >
            <EnvelopeIcon className="h-4 w-4" />
            Email Campaigns ({campaigns.length})
          </button>
        </nav>
      </div>

      {/* Promotions Tab */}
      {activeTab === 'promotions' && (
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Promotion
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Usage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-admin-border">
                {promotions.map((promotion) => (
                  <tr key={promotion.id} className="hover:bg-admin-hover">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-admin-text-primary">
                          {promotion.name}
                        </div>
                        <div className="text-sm text-admin-text-muted">
                          {promotion.value}% {promotion.type === 'discount' ? 'off' : promotion.type === 'bogo' ? 'off second item' : ''}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {getPromotionTypeLabel(promotion.type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <code className="px-2 py-1 text-xs bg-admin-bg-primary rounded border">
                        {promotion.code}
                      </code>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {promotion.usage_count}
                      {promotion.usage_limit && ` / ${promotion.usage_limit}`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(promotion.status)}`}>
                        {promotion.status.charAt(0).toUpperCase() + promotion.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-muted">
                      {new Date(promotion.start_date).toLocaleDateString()} - {new Date(promotion.end_date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button className="text-admin-text-muted hover:text-admin-text-primary" title="View">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button className="text-admin-text-muted hover:text-admin-text-primary" title="Edit">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-admin-error hover:text-admin-error/80" title="Delete">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Campaigns Tab */}
      {activeTab === 'campaigns' && (
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Campaign
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Recipients
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Open Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Click Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-admin-border">
                {campaigns.map((campaign) => (
                  <tr key={campaign.id} className="hover:bg-admin-hover">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-admin-text-primary">
                          {campaign.name}
                        </div>
                        <div className="text-sm text-admin-text-muted">
                          {campaign.subject}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {campaign.recipients.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {campaign.open_rate ? `${campaign.open_rate}%` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {campaign.click_rate ? `${campaign.click_rate}%` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(campaign.status)}`}>
                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-muted">
                      {campaign.sent_at 
                        ? new Date(campaign.sent_at).toLocaleDateString()
                        : campaign.scheduled_at 
                        ? new Date(campaign.scheduled_at).toLocaleDateString()
                        : '-'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button className="text-admin-text-muted hover:text-admin-text-primary" title="View">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {campaign.status === 'draft' && (
                          <button className="text-admin-text-muted hover:text-admin-text-primary" title="Edit">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button className="text-admin-error hover:text-admin-error/80" title="Delete">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}
