import { Link } from '@tanstack/react-router'
import { 
  ArrowLeftIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  TrashIcon
} from '@heroicons/react/24/outline'

interface ProductEditorHeaderProps {
  mode: 'create' | 'edit'
  productName: string
  isLoading: boolean
  onSaveDraft: () => void
  onPublish: () => void
  onPreview: () => void
  isPreviewMode: boolean
}

export function ProductEditorHeader({
  mode,
  productName,
  isLoading,
  onSaveDraft,
  onPublish,
  onPreview,
  isPreviewMode
}: ProductEditorHeaderProps) {
  const handleDuplicate = () => {
    console.log('Duplicate product')
    // TODO: Implement duplicate functionality
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this product?')) {
      console.log('Delete product')
      // TODO: Implement delete functionality
    }
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Link
          to="/products"
          className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
          title="Back to products"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>

        <div>
          <h1 className="text-2xl font-semibold text-admin-text-primary">
            {mode === 'create' ? 'Create Product' : 'Edit Product'}
          </h1>
          <p className="mt-1 text-sm text-admin-text-secondary">
            {productName}
          </p>
        </div>
      </div>

      <div className="flex items-center gap-3">
        {/* Preview Button */}
        <button
          type="button"
          onClick={onPreview}
          className={`flex items-center gap-2 px-3 py-2 text-sm border rounded-md transition-colors ${
            isPreviewMode
              ? 'bg-admin-primary text-white border-admin-primary'
              : 'text-admin-text-secondary border-admin-border hover:text-admin-text-primary hover:bg-admin-hover'
          }`}
        >
          <EyeIcon className="h-4 w-4" />
          {isPreviewMode ? 'Exit Preview' : 'Preview'}
        </button>

        {/* Additional Actions for Edit Mode */}
        {mode === 'edit' && (
          <>
            <button
              type="button"
              onClick={handleDuplicate}
              className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary border border-admin-border rounded-md hover:text-admin-text-primary hover:bg-admin-hover transition-colors"
              title="Duplicate product"
            >
              <DocumentDuplicateIcon className="h-4 w-4" />
              Duplicate
            </button>

            <button
              type="button"
              onClick={handleDelete}
              className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 border border-red-200 rounded-md hover:bg-red-50 transition-colors"
              title="Delete product"
            >
              <TrashIcon className="h-4 w-4" />
              Delete
            </button>
          </>
        )}

        {/* Save Actions */}
        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={onSaveDraft}
            disabled={isLoading}
            className="px-4 py-2 text-sm text-admin-text-secondary border border-admin-border rounded-md hover:text-admin-text-primary hover:bg-admin-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Draft'}
          </button>

          <button
            type="button"
            onClick={onPublish}
            disabled={isLoading}
            className="px-4 py-2 bg-admin-primary text-white text-sm font-medium rounded-md hover:bg-admin-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Publishing...' : mode === 'create' ? 'Publish Product' : 'Update & Publish'}
          </button>
        </div>
      </div>
    </div>
  )
}
