import React, { useState } from 'react'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline'

interface Alert {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  actionLabel?: string
  actionUrl?: string
  dismissible?: boolean
  priority: 'low' | 'medium' | 'high'
}

// TODO: Implement real alerts data from Supabase
const mockAlerts: Alert[] = []

const alertIcons = {
  info: InformationCircleIcon,
  warning: ExclamationTriangleIcon,
  error: XCircleIcon,
  success: CheckCircleIcon,
}

const alertColors = {
  info: 'border-admin-info bg-admin-info/5 text-admin-info',
  warning: 'border-admin-warning bg-admin-warning/5 text-admin-warning',
  error: 'border-admin-error bg-admin-error/5 text-admin-error',
  success: 'border-admin-success bg-admin-success/5 text-admin-success',
}

const priorityOrder = { high: 3, medium: 2, low: 1 }

export function AlertsPanel() {
  const [alerts, setAlerts] = useState<Alert[]>(mockAlerts)
  const [showAll, setShowAll] = useState(false)

  const dismissAlert = (id: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id))
  }

  const handleActionClick = (alert: Alert) => {
    if (alert.actionUrl) {
      // TODO: Implement navigation to action URL
    }
  }

  const sortedAlerts = [...alerts].sort((a, b) => 
    priorityOrder[b.priority] - priorityOrder[a.priority]
  )

  const displayedAlerts = showAll ? sortedAlerts : sortedAlerts.slice(0, 3)
  const highPriorityCount = alerts.filter(alert => alert.priority === 'high').length

  return (
    <div className="admin-card p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
            Alerts
          </h3>
          {highPriorityCount > 0 && (
            <span className="px-2 py-1 bg-admin-error text-white text-xs rounded-full">
              {highPriorityCount} urgent
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAll(!showAll)}
            className="p-1 text-admin-text-muted hover:text-admin-text-primary transition-colors"
            title={showAll ? 'Show less' : 'Show all'}
          >
            {showAll ? (
              <EyeSlashIcon className="h-4 w-4" />
            ) : (
              <EyeIcon className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      <div className="space-y-3">
        {displayedAlerts.length > 0 ? (
          displayedAlerts.map((alert) => {
            const Icon = alertIcons[alert.type]
            const colorClass = alertColors[alert.type]

            return (
              <div
                key={alert.id}
                className={`border rounded-lg p-4 ${colorClass}`}
              >
                <div className="flex items-start gap-3">
                  <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-admin-text-primary">
                        {alert.title}
                      </h4>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-admin-text-muted">
                          {alert.timestamp}
                        </span>
                        {alert.dismissible && (
                          <button
                            onClick={() => dismissAlert(alert.id)}
                            className="text-admin-text-muted hover:text-admin-text-primary transition-colors"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-sm text-admin-text-secondary mt-1">
                      {alert.message}
                    </p>
                    
                    {alert.actionLabel && (
                      <button
                        onClick={() => handleActionClick(alert)}
                        className="mt-2 text-sm font-medium text-admin-primary hover:text-admin-primary/80 transition-colors"
                      >
                        {alert.actionLabel} →
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )
          })
        ) : (
          <div className="text-center py-8">
            <CheckCircleIcon className="h-8 w-8 text-admin-success mx-auto mb-2" />
            <p className="text-sm text-admin-text-muted">All clear!</p>
            <p className="text-xs text-admin-text-muted mt-1">
              No alerts require your attention right now
            </p>
          </div>
        )}
      </div>

      {alerts.length > 3 && !showAll && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(true)}
            className="text-sm text-admin-primary hover:text-admin-primary/80 transition-colors"
          >
            Show {alerts.length - 3} more alerts
          </button>
        </div>
      )}
    </div>
  )
}
