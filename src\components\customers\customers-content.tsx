import React, { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { CUSTOMER_STATUSES } from '@/lib/constants'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import toast from 'react-hot-toast'

interface Customer {
  id: string
  name: string
  email: string
  phone?: string
  total_orders: number
  total_spent: number
  status: keyof typeof CUSTOMER_STATUSES
  created_at: string
  last_order_at?: string
}

export function CustomersContent() {
  const { hasPermission } = useStore()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // TODO: Implement real customer fetching from Supabase
  useEffect(() => {
    setLoading(false)
    setCustomers([])
  }, [])

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (customer.phone && customer.phone.includes(searchTerm))
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter
    return matchesSearch && matchesStatus
  })

  if (!hasPermission('view_customers')) {
    return (
      <div className="text-center py-12">
        <UserIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
        <h3 className="text-lg font-medium text-admin-text-primary mb-2">
          Access Denied
        </h3>
        <p className="text-admin-text-muted">
          You don't have permission to view customers.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-admin-text-primary">
            Customers
          </h1>
          <p className="text-admin-text-muted mt-1">
            Manage customer relationships and track purchase history
          </p>
        </div>
        {hasPermission('manage_customers') && (
          <Button className="flex items-center gap-2">
            <PlusIcon className="h-4 w-4" />
            Add Customer
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserIcon className="h-8 w-8 text-admin-primary" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Total Customers</p>
              <p className="text-2xl font-bold text-admin-text-primary">{customers.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserIcon className="h-8 w-8 text-admin-success" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Active Customers</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {customers.filter(c => c.status === 'active').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-8 w-8 text-admin-info" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">New This Month</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                {customers.filter(c => new Date(c.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-admin-warning rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">$</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-admin-text-muted">Avg. Order Value</p>
              <p className="text-2xl font-bold text-admin-text-primary">
                ${customers.reduce((acc, c) => acc + c.total_spent, 0) / Math.max(customers.filter(c => c.total_orders > 0).length, 1) || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-admin-text-muted" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-admin-border rounded-md bg-admin-bg-secondary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
        >
          <option value="all">All Status</option>
          {Object.entries(CUSTOMER_STATUSES).map(([key, status]) => (
            <option key={key} value={key}>
              {status.label}
            </option>
          ))}
        </select>
        <Button variant="ghost" className="flex items-center gap-2">
          <FunnelIcon className="h-4 w-4" />
          More Filters
        </Button>
      </div>

      {/* Customers Table */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-primary mx-auto"></div>
          <p className="text-admin-text-muted mt-2">Loading customers...</p>
        </div>
      ) : (
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Orders
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Total Spent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Last Order
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-muted uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-admin-border">
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-admin-hover">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-admin-primary rounded-full flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {customer.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-admin-text-primary">
                            {customer.name}
                          </div>
                          <div className="text-sm text-admin-text-muted">
                            Customer since {new Date(customer.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-admin-text-primary">
                          <EnvelopeIcon className="h-4 w-4 mr-2 text-admin-text-muted" />
                          {customer.email}
                        </div>
                        {customer.phone && (
                          <div className="flex items-center text-sm text-admin-text-muted">
                            <PhoneIcon className="h-4 w-4 mr-2" />
                            {customer.phone}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-primary">
                      {customer.total_orders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-admin-text-primary">
                      ${customer.total_spent.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        customer.status === 'active' ? 'bg-admin-success/10 text-admin-success' :
                        customer.status === 'inactive' ? 'bg-admin-warning/10 text-admin-warning' :
                        'bg-admin-error/10 text-admin-error'
                      }`}>
                        {CUSTOMER_STATUSES[customer.status].label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-admin-text-muted">
                      {customer.last_order_at 
                        ? new Date(customer.last_order_at).toLocaleDateString()
                        : 'Never'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          className="text-admin-text-muted hover:text-admin-text-primary"
                          title="View customer"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {hasPermission('manage_customers') && (
                          <button
                            className="text-admin-text-muted hover:text-admin-text-primary"
                            title="Edit customer"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {filteredCustomers.length === 0 && !loading && (
        <div className="text-center py-12">
          <UserIcon className="h-12 w-12 text-admin-text-muted mx-auto mb-4" />
          <h3 className="text-lg font-medium text-admin-text-primary mb-2">
            No customers found
          </h3>
          <p className="text-admin-text-muted">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Customers will appear here once they start placing orders'
            }
          </p>
        </div>
      )}
    </div>
  )
}
