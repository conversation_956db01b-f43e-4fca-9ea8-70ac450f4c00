import React, { useState } from 'react'
import { 
  ShieldCheckIcon, 
  UserGroupIcon, 
  KeyIcon, 
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { UserRole } from '../../types/user'
import { PermissionsService, ROLE_PERMISSIONS, PERMISSIONS } from '../../services/permissions-service'

interface AccessPermissionsTabProps {
  currentUserRole: UserRole
}

export const AccessPermissionsTab: React.FC<AccessPermissionsTabProps> = ({
  currentUserRole
}) => {
  const [selectedRole, setSelectedRole] = useState<UserRole>('viewer')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  const permissionsByCategory = PermissionsService.getPermissionsByCategory()
  const selectedRolePermissions = PermissionsService.getRolePermissions(selectedRole)

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const getRoleColor = (role: UserRole): string => {
    const colors = {
      super_admin: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      admin: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      editor: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      viewer: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
    return colors[role]
  }

  const getRoleIcon = (role: UserRole) => {
    const icons = {
      super_admin: <ShieldCheckIcon className="h-5 w-5" />,
      admin: <UserGroupIcon className="h-5 w-5" />,
      editor: <PencilIcon className="h-5 w-5" />,
      viewer: <EyeIcon className="h-5 w-5" />
    }
    return icons[role]
  }

  const getPermissionIcon = (permissionId: string) => {
    if (permissionId.includes('view')) return <EyeIcon className="h-4 w-4" />
    if (permissionId.includes('edit') || permissionId.includes('create')) return <PencilIcon className="h-4 w-4" />
    if (permissionId.includes('delete')) return <TrashIcon className="h-4 w-4" />
    if (permissionId.includes('manage')) return <KeyIcon className="h-4 w-4" />
    return <CheckCircleIcon className="h-4 w-4" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Access & Permissions
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Manage user roles and their access permissions
            </p>
          </div>
        </div>

        {/* Role Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {ROLE_PERMISSIONS.map((roleData) => (
            <div
              key={roleData.role}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedRole === roleData.role
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setSelectedRole(roleData.role)}
            >
              <div className="flex items-center space-x-2 mb-2">
                {getRoleIcon(roleData.role)}
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(roleData.role)}`}>
                  {roleData.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {roleData.description}
              </p>
              <div className="text-xs text-gray-500 dark:text-gray-500">
                {roleData.permissions.length} permissions
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Role Permissions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getRoleIcon(selectedRole)}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {selectedRole.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Permissions
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {PermissionsService.getRoleDescription(selectedRole)}
                </p>
              </div>
            </div>
            <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getRoleColor(selectedRole)}`}>
              {selectedRolePermissions.length} permissions
            </span>
          </div>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {Object.entries(permissionsByCategory).map(([category, permissions]) => (
              <div key={category} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                <button
                  onClick={() => toggleCategory(category)}
                  className="w-full px-4 py-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-t-lg flex items-center justify-between"
                >
                  <span className="font-medium text-gray-900 dark:text-white">{category}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {permissions.filter(p => selectedRolePermissions.includes(p.id)).length} / {permissions.length}
                    </span>
                    <svg
                      className={`h-5 w-5 text-gray-400 transition-transform ${
                        expandedCategories.has(category) ? 'rotate-180' : ''
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {expandedCategories.has(category) && (
                  <div className="p-4 space-y-2">
                    {permissions.map((permission) => {
                      const hasPermission = selectedRolePermissions.includes(permission.id)
                      return (
                        <div
                          key={permission.id}
                          className={`flex items-center space-x-3 p-3 rounded-lg ${
                            hasPermission
                              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                              : 'bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600'
                          }`}
                        >
                          <div className={`flex-shrink-0 ${hasPermission ? 'text-green-600' : 'text-gray-400'}`}>
                            {hasPermission ? (
                              <CheckCircleIcon className="h-5 w-5" />
                            ) : (
                              <XCircleIcon className="h-5 w-5" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              {getPermissionIcon(permission.id)}
                              <span className={`font-medium ${
                                hasPermission ? 'text-green-900 dark:text-green-100' : 'text-gray-900 dark:text-white'
                              }`}>
                                {permission.name}
                              </span>
                            </div>
                            <p className={`text-sm ${
                              hasPermission ? 'text-green-700 dark:text-green-300' : 'text-gray-600 dark:text-gray-400'
                            }`}>
                              {permission.description}
                            </p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Role Comparison */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Role Comparison
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-2 px-3 text-sm font-medium text-gray-900 dark:text-white">
                  Permission Category
                </th>
                {ROLE_PERMISSIONS.map((roleData) => (
                  <th key={roleData.role} className="text-center py-2 px-3">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(roleData.role)}`}>
                      {roleData.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Object.entries(permissionsByCategory).map(([category, permissions]) => (
                <tr key={category} className="border-b border-gray-100 dark:border-gray-700">
                  <td className="py-3 px-3 text-sm font-medium text-gray-900 dark:text-white">
                    {category}
                  </td>
                  {ROLE_PERMISSIONS.map((roleData) => {
                    const rolePermissions = roleData.permissions
                    const categoryPermissions = permissions.filter(p => rolePermissions.includes(p.id))
                    const percentage = Math.round((categoryPermissions.length / permissions.length) * 100)
                    
                    return (
                      <td key={roleData.role} className="py-3 px-3 text-center">
                        <div className="flex items-center justify-center space-x-1">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {categoryPermissions.length}/{permissions.length}
                          </span>
                          <div className={`h-2 w-8 rounded-full ${
                            percentage === 100 ? 'bg-green-500' :
                            percentage >= 50 ? 'bg-yellow-500' :
                            percentage > 0 ? 'bg-orange-500' : 'bg-gray-300'
                          }`} />
                        </div>
                      </td>
                    )
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Current User Info */}
      {currentUserRole && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-900 dark:text-blue-100">
              Your Current Role:
            </span>
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(currentUserRole)}`}>
              {currentUserRole.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
          </div>
          <p className="mt-2 text-sm text-blue-700 dark:text-blue-300">
            {PermissionsService.getRoleDescription(currentUserRole)}
          </p>
        </div>
      )}
    </div>
  )
}
