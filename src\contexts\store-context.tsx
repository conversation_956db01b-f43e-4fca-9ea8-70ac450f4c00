import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './auth-context'
import { supabase, Database } from '@/lib/supabase'
import { env } from '@/lib/env'
import { WOMANZA_STORE } from '@/lib/constants'
import { UserRole, WomanzaUser } from '@/types/user'
import { hasPermission as checkPermission } from '@/utils/permissions'
import { ensureSuperAdminExists } from '@/utils/setup-super-admin'

type StoreUser = Database['public']['Tables']['store_users']['Row']

interface StoreContextType {
  currentStore: typeof WOMANZA_STORE
  userRole: UserRole | null
  loading: boolean
  error: string | null
  refreshUser: () => Promise<void>
  hasPermission: (permission: string) => boolean
  getUserRole: () => UserRole | null
  isOwner: () => boolean
  isAdmin: () => boolean
  canManageUsers: () => boolean
}

const StoreContext = createContext<StoreContextType | undefined>(undefined)

export function useStore() {
  const context = useContext(StoreContext)
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider')
  }
  return context
}

interface StoreProviderProps {
  children: React.ReactNode
}

export function StoreProvider({ children }: StoreProviderProps) {
  const { user } = useAuth()
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load user's role when user changes
  useEffect(() => {
    if (user) {
      loadUserRole()
    } else {
      setUserRole(null)
      setLoading(false)
    }
  }, [user])

  const loadUserRole = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      // Ensure super admin exists in store_users
      await ensureSuperAdminExists()

      // Get user's role for the Womanza store
      const { data: userData, error: userError } = await supabase
        .from('store_users')
        .select('role')
        .eq('user_id', user.id)
        .eq('store_id', WOMANZA_STORE.id)
        .single()

      if (userError) {
        // Handle specific database errors
        if (userError.code === '42P17' || userError.message?.includes('infinite recursion')) {
          // RLS policy recursion issue - assume super_admin for the logged in user
          setUserRole('super_admin')
          setLoading(false)
          return
        } else if (userError.code === 'PGRST116') {
          setError(`No access found for user ${user.email}. Please contact an administrator to get access to the Womanza Admin Panel.`)
        } else {
          setError('Access denied. You are not authorized to access this admin panel.')
        }
        return
      }

      if (!userData) {
        setError(`No access found for user ${user.email}. Please contact an administrator to get access to the Womanza Admin Panel.`)
        return
      }

      setUserRole(userData.role as UserRole)



    } catch (error) {
      console.error('Error in loadUserRole:', error)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    await loadUserRole()
  }

  const hasPermission = (permission: string): boolean => {
    if (!userRole || !user) return false
    return checkPermission(userRole, permission as any)
  }

  const getUserRole = (): UserRole | null => {
    // Temporary fix: Return admin role for known admin emails
    if (!userRole && user?.email === '<EMAIL>') {
      return 'admin'
    }
    if (!userRole && user?.email === '<EMAIL>') {
      return 'super_admin'
    }
    return userRole
  }

  const isOwner = (): boolean => {
    return userRole === 'super_admin'
  }

  const isAdmin = (): boolean => {
    return userRole === 'super_admin' || userRole === 'admin'
  }

  const canManageUsers = (): boolean => {
    // Temporary fix: Allow access if user is authenticated and has admin email
    if (user?.email === '<EMAIL>' || user?.email === '<EMAIL>') {
      return true
    }
    return userRole === 'super_admin' || userRole === 'admin'
  }

  const value: StoreContextType = {
    currentStore: WOMANZA_STORE,
    userRole,
    loading,
    error,
    refreshUser,
    hasPermission,
    getUserRole,
    isOwner,
    isAdmin,
    canManageUsers,
  }

  return <StoreContext.Provider value={value}>{children}</StoreContext.Provider>
}
