import React, { useState, useEffect } from 'react'
import { 
  BellIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'

interface Notification {
  id: string
  type: 'info' | 'warning' | 'success' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionUrl?: string
}

// TODO: Implement real notifications from Supabase
const mockNotifications: Notification[] = []

const notificationIcons = {
  info: InformationCircleIcon,
  warning: ExclamationTriangleIcon,
  success: CheckCircleIcon,
  error: ExclamationTriangleIcon,
}

const notificationColors = {
  info: 'text-admin-info',
  warning: 'text-admin-warning',
  success: 'text-admin-success',
  error: 'text-admin-error',
}

export function NotificationBell() {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications)
  const [showDropdown, setShowDropdown] = useState(false)
  const { currentStore } = useStore()

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    )
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id)
    if (notification.actionUrl) {
      // In a real app, you would navigate to the action URL
      console.log('Navigate to:', notification.actionUrl)
    }
    setShowDropdown(false)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDropdown) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showDropdown])

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowDropdown(!showDropdown)}
        className="p-2 hover:bg-admin-hover relative"
        title="Notifications"
      >
        <BellIcon className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-admin-error text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </Button>

      {showDropdown && (
        <div className="absolute top-full right-0 mt-1 w-80 bg-admin-bg-secondary border border-admin-border rounded-lg shadow-lg z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-admin-border">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-admin-text-primary">
                Notifications
              </h3>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-admin-primary hover:text-admin-primary/80"
                >
                  Mark all as read
                </button>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {notifications.length > 0 ? (
              <div className="py-2">
                {notifications.map((notification) => {
                  const Icon = notificationIcons[notification.type]
                  const iconColor = notificationColors[notification.type]
                  
                  return (
                    <div
                      key={notification.id}
                      className={`relative px-4 py-3 hover:bg-admin-hover transition-colors ${
                        !notification.read ? 'bg-admin-primary/5' : ''
                      }`}
                    >
                      <button
                        onClick={() => handleNotificationClick(notification)}
                        className="w-full text-left"
                      >
                        <div className="flex items-start gap-3">
                          <Icon className={`h-5 w-5 ${iconColor} flex-shrink-0 mt-0.5`} />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-admin-text-primary truncate">
                                {notification.title}
                              </p>
                              {!notification.read && (
                                <div className="h-2 w-2 bg-admin-primary rounded-full flex-shrink-0 ml-2" />
                              )}
                            </div>
                            <p className="text-xs text-admin-text-muted mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-admin-text-muted mt-1">
                              {notification.timestamp}
                            </p>
                          </div>
                        </div>
                      </button>
                      
                      <button
                        onClick={() => removeNotification(notification.id)}
                        className="absolute top-2 right-2 p-1 text-admin-text-muted hover:text-admin-text-primary opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </button>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="px-4 py-8 text-center">
                <BellIcon className="h-8 w-8 text-admin-text-muted mx-auto mb-2" />
                <p className="text-sm text-admin-text-muted">No notifications</p>
                <p className="text-xs text-admin-text-muted mt-1">
                  You're all caught up!
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="px-4 py-3 border-t border-admin-border">
              <button className="w-full text-center text-sm text-admin-primary hover:text-admin-primary/80">
                View all notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
