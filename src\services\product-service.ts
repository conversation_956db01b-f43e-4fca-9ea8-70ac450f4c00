import { supabase } from '@/lib/supabase'
import type { 
  Product, 
  Category, 
  ProductFilter, 
  ProductFormData, 
  BulkProductAction,
  ProductStats 
} from '@/types/product'

export class ProductService {
  // Product CRUD operations
  static async getProducts(storeId: string, filters: ProductFilter = {}) {
    // Try with categories join first, fallback to simple query if it fails
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug)
      `)
      .eq('store_id', storeId)

    // Apply filters
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,sku.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }

    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id)
    }

    if (filters.status) {
      query = query.eq('status', filters.status)
    }

    if (filters.is_featured !== undefined) {
      query = query.eq('is_featured', filters.is_featured)
    }

    if (filters.low_stock) {
      query = query.lt('inventory_quantity', supabase.raw('low_stock_threshold'))
    }

    if (filters.price_min !== undefined) {
      query = query.gte('price', filters.price_min)
    }

    if (filters.price_max !== undefined) {
      query = query.lte('price', filters.price_max)
    }

    if (filters.metal_type) {
      query = query.eq('metal_type', filters.metal_type)
    }

    if (filters.gemstone_type) {
      query = query.eq('gemstone_type', filters.gemstone_type)
    }

    // Sorting
    const sortBy = filters.sort_by || 'created_at'
    const sortOrder = filters.sort_order || 'desc'
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // Pagination
    const page = filters.page || 1
    const limit = filters.limit || 20
    const from = (page - 1) * limit
    const to = from + limit - 1

    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      // If categories table doesn't exist, try without the join
      if (error.message?.includes('categories') || error.code === '42P01') {
        let fallbackQuery = supabase
          .from('products')
          .select('*')
          .eq('store_id', storeId)

        // Apply same filters without category join
        if (filters.search) {
          fallbackQuery = fallbackQuery.or(`name.ilike.%${filters.search}%,sku.ilike.%${filters.search}%`)
        }

        if (filters.status) {
          fallbackQuery = fallbackQuery.eq('status', filters.status)
        }

        if (filters.is_featured !== undefined) {
          fallbackQuery = fallbackQuery.eq('is_featured', filters.is_featured)
        }

        // Sorting
        const sortBy = filters.sort_by || 'created_at'
        const sortOrder = filters.sort_order || 'desc'
        fallbackQuery = fallbackQuery.order(sortBy, { ascending: sortOrder === 'asc' })

        // Pagination
        const page = filters.page || 1
        const limit = filters.limit || 20
        const from = (page - 1) * limit
        const to = from + limit - 1
        fallbackQuery = fallbackQuery.range(from, to)

        const fallbackResult = await fallbackQuery
        if (fallbackResult.error) throw fallbackResult.error

        return {
          data: (fallbackResult.data as Product[]).map(product => ({
            ...product,
            category: null // No category data available
          })),
          count: fallbackResult.count || 0,
          page,
          limit,
          totalPages: Math.ceil((fallbackResult.count || 0) / limit)
        }
      }
      throw error
    }

    return {
      data: data as Product[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    }
  }

  static async getProduct(id: string) {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Product
  }

  static async createProduct(storeId: string, productData: ProductFormData) {
    const { data, error } = await supabase
      .from('products')
      .insert({
        ...productData,
        store_id: storeId,
      })
      .select()
      .single()

    if (error) throw error
    return data as Product
  }

  static async updateProduct(id: string, productData: Partial<ProductFormData>) {
    const { data, error } = await supabase
      .from('products')
      .update({
        ...productData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Product
  }

  static async deleteProduct(id: string) {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  static async bulkUpdateProducts(action: BulkProductAction) {
    const { product_ids, data: updateData } = action

    let query = supabase
      .from('products')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .in('id', product_ids)

    const { error } = await query

    if (error) throw error
  }

  static async bulkDeleteProducts(productIds: string[]) {
    const { error } = await supabase
      .from('products')
      .delete()
      .in('id', productIds)

    if (error) throw error
  }

  // Category operations
  static async getCategories(storeId: string) {
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        products_count:products(count)
      `)
      .eq('store_id', storeId)
      .order('name')

    if (error) {
      // If categories table doesn't exist, return empty array
      if (error.code === '42P01' || error.message?.includes('categories')) {
        return []
      }
      throw error
    }
    return data as Category[]
  }

  static async createCategory(storeId: string, categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...categoryData,
        store_id: storeId,
      })
      .select()
      .single()

    if (error) throw error
    return data as Category
  }

  static async updateCategory(id: string, categoryData: Partial<Category>) {
    const { data, error } = await supabase
      .from('categories')
      .update({
        ...categoryData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Category
  }

  static async deleteCategory(id: string) {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Analytics and stats
  static async getProductStats(storeId: string): Promise<ProductStats> {
    const { data, error } = await supabase
      .from('products')
      .select('status, is_featured, price, inventory_quantity, low_stock_threshold')
      .eq('store_id', storeId)

    if (error) throw error

    const products = data || []
    
    const stats: ProductStats = {
      total_products: products.length,
      active_products: products.filter(p => p.status === 'active').length,
      draft_products: products.filter(p => p.status === 'draft').length,
      low_stock_products: products.filter(p => p.inventory_quantity <= p.low_stock_threshold).length,
      out_of_stock_products: products.filter(p => p.inventory_quantity === 0).length,
      featured_products: products.filter(p => p.is_featured).length,
      total_value: products.reduce((sum, p) => sum + (p.price * p.inventory_quantity), 0),
      average_price: products.length > 0 ? products.reduce((sum, p) => sum + p.price, 0) / products.length : 0,
    }

    return stats
  }

  // Utility functions
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  static async checkSkuExists(storeId: string, sku: string, excludeId?: string): Promise<boolean> {
    let query = supabase
      .from('products')
      .select('id')
      .eq('store_id', storeId)
      .eq('sku', sku)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error
    return (data?.length || 0) > 0
  }

  static async checkSlugExists(storeId: string, slug: string, excludeId?: string): Promise<boolean> {
    let query = supabase
      .from('products')
      .select('id')
      .eq('store_id', storeId)
      .eq('slug', slug)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) throw error
    return (data?.length || 0) > 0
  }
}
