-- 🚀 Minimal Users Setup - Just the basics
-- Run this script in your Supabase SQL Editor

-- Clean start
DROP TABLE IF EXISTS users CASCADE;

-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS with permissive policy for now
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Allow all operations for authenticated users (we'll tighten this later)
CREATE POLICY "Allow all for authenticated users" ON users
    FOR ALL USING (auth.role() = 'authenticated');

-- Success message
SELECT 'Users table created successfully!' as status;
