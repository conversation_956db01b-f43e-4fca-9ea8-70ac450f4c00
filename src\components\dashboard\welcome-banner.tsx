import React from 'react'
import {
  SunIcon,
  MoonIcon,
  SparklesIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
} from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/auth-context'
import { useStore } from '@/contexts/store-context'
import { useDashboardStats } from '@/hooks/use-analytics'

export function WelcomeBanner() {
  const { user } = useAuth()
  const { currentStore } = useStore()
  const { data: stats } = useDashboardStats()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 18) return 'Good afternoon'
    return 'Good evening'
  }

  const getGreetingIcon = () => {
    const hour = new Date().getHours()
    if (hour >= 6 && hour < 18) {
      return <SunIcon className="h-5 w-5 text-yellow-500" />
    }
    return <MoonIcon className="h-5 w-5 text-blue-400" />
  }

  const userName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Admin'

  const todayStats = [
    {
      label: 'Today\'s Revenue',
      value: stats ? `$${stats.totalRevenue.toLocaleString()}` : '$0',
      change: stats?.revenueChange || 0,
      icon: ChartBarIcon,
    },
    {
      label: 'Orders Today',
      value: stats ? stats.totalOrders.toString() : '0',
      change: stats?.ordersChange || 0,
      icon: ArrowTrendingUpIcon,
    },
  ]

  return (
    <div className="admin-card p-6 bg-gradient-to-r from-admin-primary/5 to-admin-accent/5 border-admin-primary/20">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getGreetingIcon()}
          <div>
            <h2 className="text-xl font-serif font-bold text-admin-text-primary">
              {getGreeting()}, {userName}!
            </h2>
            <p className="text-admin-text-secondary mt-1">
              Welcome back to {currentStore?.name || 'your store'}. Here's what's happening today.
            </p>
          </div>
        </div>
        
        <div className="hidden md:flex items-center gap-6">
          {todayStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <stat.icon className="h-4 w-4 text-admin-primary" />
                <span className="text-lg font-semibold text-admin-text-primary">
                  {stat.value}
                </span>
              </div>
              <p className="text-xs text-admin-text-muted">{stat.label}</p>
              {stat.change !== 0 && (
                <p className={`text-xs ${
                  stat.change > 0 ? 'text-admin-success' : 'text-admin-error'
                }`}>
                  {stat.change > 0 ? '+' : ''}{stat.change.toFixed(1)}%
                </p>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Quick Insights */}
      <div className="mt-4 flex items-center gap-2 text-sm text-admin-text-secondary">
        <SparklesIcon className="h-4 w-4 text-admin-accent" />
        <span>
          {stats && stats.totalOrders > 0 
            ? `You've processed ${stats.totalOrders} orders this month. Keep up the great work!`
            : 'Ready to start processing orders? Your dashboard will show insights as data comes in.'
          }
        </span>
      </div>
    </div>
  )
}
