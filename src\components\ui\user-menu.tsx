import React, { useState } from 'react'
import { User } from '@supabase/supabase-js'
import { ChevronDownIcon, UserIcon, CogIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/auth-context'
import { Button } from './button'
import toast from 'react-hot-toast'

interface UserMenuProps {
  user: User | null
}

export function UserMenu({ user }: UserMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const { signOut } = useAuth()

  const handleSignOut = async () => {
    const { error } = await signOut()
    if (error) {
      toast.error('Failed to sign out')
    } else {
      toast.success('Signed out successfully')
      window.location.href = '/login'
    }
  }

  if (!user) return null

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-2"
      >
        <div className="w-8 h-8 bg-admin-primary rounded-full flex items-center justify-center">
          <span className="text-white text-sm font-medium">
            {user.email?.charAt(0).toUpperCase()}
          </span>
        </div>
        <span className="text-sm text-admin-text-primary hidden md:block">
          {user.email}
        </span>
        <ChevronDownIcon className="h-4 w-4 text-admin-text-muted" />
      </Button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-2 w-48 bg-admin-bg-secondary border border-admin-border rounded-md shadow-lg z-20">
            <div className="py-1">
              <button
                className="flex items-center w-full px-4 py-2 text-sm text-admin-text-primary hover:bg-admin-hover"
                onClick={() => setIsOpen(false)}
              >
                <UserIcon className="h-4 w-4 mr-3" />
                Profile
              </button>
              <button
                className="flex items-center w-full px-4 py-2 text-sm text-admin-text-primary hover:bg-admin-hover"
                onClick={() => setIsOpen(false)}
              >
                <CogIcon className="h-4 w-4 mr-3" />
                Settings
              </button>
              <hr className="my-1 border-admin-border" />
              <button
                className="flex items-center w-full px-4 py-2 text-sm text-admin-error hover:bg-admin-hover"
                onClick={handleSignOut}
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                Sign out
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
