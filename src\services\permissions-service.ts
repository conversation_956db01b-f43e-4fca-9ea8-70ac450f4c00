import { UserRole } from '../types/user'

export interface Permission {
  id: string
  name: string
  description: string
  category: string
}

export interface RolePermissions {
  role: UserRole
  permissions: string[]
  description: string
}

// Define all available permissions
export const PERMISSIONS: Permission[] = [
  // User Management
  { id: 'users.view', name: 'View Users', description: 'View user list and details', category: 'User Management' },
  { id: 'users.create', name: 'Create Users', description: 'Invite new users to the system', category: 'User Management' },
  { id: 'users.edit', name: 'Edit Users', description: 'Edit user roles and information', category: 'User Management' },
  { id: 'users.delete', name: 'Delete Users', description: 'Remove users from the system', category: 'User Management' },
  { id: 'users.manage_roles', name: 'Manage Roles', description: 'Change user roles and permissions', category: 'User Management' },
  
  // Product Management
  { id: 'products.view', name: 'View Products', description: 'View product catalog and details', category: 'Product Management' },
  { id: 'products.create', name: 'Create Products', description: 'Add new products to catalog', category: 'Product Management' },
  { id: 'products.edit', name: 'Edit Products', description: 'Modify existing products', category: 'Product Management' },
  { id: 'products.delete', name: 'Delete Products', description: 'Remove products from catalog', category: 'Product Management' },
  { id: 'products.manage_categories', name: 'Manage Categories', description: 'Create and edit product categories', category: 'Product Management' },
  
  // Inventory Management
  { id: 'inventory.view', name: 'View Inventory', description: 'View stock levels and inventory reports', category: 'Inventory Management' },
  { id: 'inventory.edit', name: 'Edit Inventory', description: 'Update stock levels and inventory data', category: 'Inventory Management' },
  { id: 'inventory.reports', name: 'Inventory Reports', description: 'Generate inventory reports and analytics', category: 'Inventory Management' },
  
  // Order Management
  { id: 'orders.view', name: 'View Orders', description: 'View order list and details', category: 'Order Management' },
  { id: 'orders.edit', name: 'Edit Orders', description: 'Modify order status and details', category: 'Order Management' },
  { id: 'orders.delete', name: 'Delete Orders', description: 'Cancel or remove orders', category: 'Order Management' },
  { id: 'orders.reports', name: 'Order Reports', description: 'Generate order reports and analytics', category: 'Order Management' },
  
  // Customer Management
  { id: 'customers.view', name: 'View Customers', description: 'View customer list and profiles', category: 'Customer Management' },
  { id: 'customers.edit', name: 'Edit Customers', description: 'Modify customer information', category: 'Customer Management' },
  { id: 'customers.delete', name: 'Delete Customers', description: 'Remove customer accounts', category: 'Customer Management' },
  
  // Analytics & Reports
  { id: 'analytics.view', name: 'View Analytics', description: 'Access dashboard analytics and metrics', category: 'Analytics & Reports' },
  { id: 'analytics.advanced', name: 'Advanced Analytics', description: 'Access detailed reports and insights', category: 'Analytics & Reports' },
  { id: 'analytics.export', name: 'Export Data', description: 'Export reports and data files', category: 'Analytics & Reports' },
  
  // Store Settings
  { id: 'settings.view', name: 'View Settings', description: 'View store configuration and settings', category: 'Store Settings' },
  { id: 'settings.edit', name: 'Edit Settings', description: 'Modify store settings and configuration', category: 'Store Settings' },
  { id: 'settings.advanced', name: 'Advanced Settings', description: 'Access advanced store configuration', category: 'Store Settings' },
  
  // Marketing
  { id: 'marketing.view', name: 'View Marketing', description: 'View marketing campaigns and tools', category: 'Marketing' },
  { id: 'marketing.create', name: 'Create Campaigns', description: 'Create and manage marketing campaigns', category: 'Marketing' },
  { id: 'marketing.edit', name: 'Edit Campaigns', description: 'Modify existing marketing campaigns', category: 'Marketing' },
  
  // System Administration
  { id: 'system.logs', name: 'View System Logs', description: 'Access system logs and audit trails', category: 'System Administration' },
  { id: 'system.backup', name: 'System Backup', description: 'Create and manage system backups', category: 'System Administration' },
  { id: 'system.maintenance', name: 'System Maintenance', description: 'Perform system maintenance tasks', category: 'System Administration' }
]

// Define role-based permissions
export const ROLE_PERMISSIONS: RolePermissions[] = [
  {
    role: 'super_admin',
    description: 'Full system access with all permissions',
    permissions: PERMISSIONS.map(p => p.id) // All permissions
  },
  {
    role: 'admin',
    description: 'Administrative access with most permissions except system administration',
    permissions: [
      // User Management (except advanced user management)
      'users.view', 'users.create', 'users.edit', 'users.manage_roles',
      
      // Product Management (full access)
      'products.view', 'products.create', 'products.edit', 'products.delete', 'products.manage_categories',
      
      // Inventory Management (full access)
      'inventory.view', 'inventory.edit', 'inventory.reports',
      
      // Order Management (full access)
      'orders.view', 'orders.edit', 'orders.delete', 'orders.reports',
      
      // Customer Management (full access)
      'customers.view', 'customers.edit', 'customers.delete',
      
      // Analytics & Reports (full access)
      'analytics.view', 'analytics.advanced', 'analytics.export',
      
      // Store Settings (most settings)
      'settings.view', 'settings.edit',
      
      // Marketing (full access)
      'marketing.view', 'marketing.create', 'marketing.edit'
    ]
  },
  {
    role: 'editor',
    description: 'Content management access with editing capabilities',
    permissions: [
      // User Management (view only)
      'users.view',
      
      // Product Management (full access)
      'products.view', 'products.create', 'products.edit', 'products.delete', 'products.manage_categories',
      
      // Inventory Management (edit access)
      'inventory.view', 'inventory.edit',
      
      // Order Management (view and edit)
      'orders.view', 'orders.edit',
      
      // Customer Management (view and edit)
      'customers.view', 'customers.edit',
      
      // Analytics & Reports (basic access)
      'analytics.view',
      
      // Store Settings (view only)
      'settings.view',
      
      // Marketing (view and edit)
      'marketing.view', 'marketing.edit'
    ]
  },
  {
    role: 'viewer',
    description: 'Read-only access to view data and reports',
    permissions: [
      // User Management (view only)
      'users.view',
      
      // Product Management (view only)
      'products.view',
      
      // Inventory Management (view only)
      'inventory.view',
      
      // Order Management (view only)
      'orders.view',
      
      // Customer Management (view only)
      'customers.view',
      
      // Analytics & Reports (basic view)
      'analytics.view',
      
      // Store Settings (view only)
      'settings.view',
      
      // Marketing (view only)
      'marketing.view'
    ]
  }
]

export class PermissionsService {
  /**
   * Check if a user role has a specific permission
   */
  static hasPermission(userRole: UserRole, permission: string): boolean {
    const rolePermissions = ROLE_PERMISSIONS.find(rp => rp.role === userRole)
    return rolePermissions?.permissions.includes(permission) || false
  }

  /**
   * Get all permissions for a specific role
   */
  static getRolePermissions(role: UserRole): string[] {
    const rolePermissions = ROLE_PERMISSIONS.find(rp => rp.role === role)
    return rolePermissions?.permissions || []
  }

  /**
   * Get permission details by ID
   */
  static getPermissionDetails(permissionId: string): Permission | undefined {
    return PERMISSIONS.find(p => p.id === permissionId)
  }

  /**
   * Get all permissions grouped by category
   */
  static getPermissionsByCategory(): Record<string, Permission[]> {
    return PERMISSIONS.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = []
      }
      acc[permission.category].push(permission)
      return acc
    }, {} as Record<string, Permission[]>)
  }

  /**
   * Get role description
   */
  static getRoleDescription(role: UserRole): string {
    const rolePermissions = ROLE_PERMISSIONS.find(rp => rp.role === role)
    return rolePermissions?.description || ''
  }

  /**
   * Check if user can access a specific route/page
   */
  static canAccessRoute(userRole: UserRole, route: string): boolean {
    const routePermissions: Record<string, string[]> = {
      '/users': ['users.view'],
      '/products': ['products.view'],
      '/catalog': ['products.view'],
      '/inventory': ['inventory.view'],
      '/orders': ['orders.view'],
      '/customers': ['customers.view'],
      '/analytics': ['analytics.view'],
      '/dashboard': ['analytics.view'],
      '/settings': ['settings.view'],
      '/marketing': ['marketing.view']
    }

    const requiredPermissions = routePermissions[route]
    if (!requiredPermissions) return true // No specific permissions required

    return requiredPermissions.some(permission => 
      this.hasPermission(userRole, permission)
    )
  }

  /**
   * Get accessible routes for a user role
   */
  static getAccessibleRoutes(userRole: UserRole): string[] {
    const allRoutes = [
      '/dashboard', '/users', '/products', '/catalog', '/inventory', 
      '/orders', '/customers', '/analytics', '/settings', '/marketing'
    ]

    return allRoutes.filter(route => this.canAccessRoute(userRole, route))
  }
}
