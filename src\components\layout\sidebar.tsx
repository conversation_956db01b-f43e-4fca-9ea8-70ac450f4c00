import React, { useState, useEffect } from 'react'
import { Link, useLocation } from '@tanstack/react-router'
import {
  ChartBarIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  CogIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  TagIcon,
  MegaphoneIcon,
  CubeIcon,
  UserGroupIcon,
  PaintBrushIcon,
  PlusIcon,
  ArchiveBoxIcon,
  ChartPieIcon,
  DocumentChartBarIcon,
  BuildingStorefrontIcon,
} from '@heroicons/react/24/outline'
import { clsx } from 'clsx'
import { useStore } from '@/contexts/store-context'
import { hasPermission, Permission } from '@/utils/permissions'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  status?: 'warning' | 'error' | 'info'
  permission?: Permission
  children?: NavigationItem[]
}



interface SidebarProps {
  collapsed?: boolean
  onToggleCollapse?: () => void
}

export function Sidebar({ collapsed = false, onToggleCollapse }: SidebarProps) {
  const location = useLocation()
  const { currentStore, getUserRole, canManageUsers, loading, error, hasPermission } = useStore()
  const [expandedItems, setExpandedItems] = useState<string[]>(['Catalog'])
  const userRole = getUserRole()

  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: ChartBarIcon,
      permission: 'view_dashboard'
    },
    {
      name: 'Catalog',
      href: '/catalog',
      icon: ShoppingBagIcon,
      badge: 2, // Low stock items
      status: 'warning',
      permission: 'view_products',
      children: [
        { name: 'All Products', href: '/catalog', icon: ShoppingBagIcon, permission: 'view_products' },
        { name: 'Add Product', href: '/catalog/add', icon: PlusIcon, permission: 'create_products' },
        { name: 'Categories & Tags', href: '/catalog/categories', icon: TagIcon, permission: 'manage_categories' },
        { name: 'Inventory', href: '/catalog/inventory', icon: ArchiveBoxIcon, permission: 'manage_inventory' },
      ]
    },
    {
      name: 'Orders',
      href: '/orders',
      icon: ClipboardDocumentListIcon,
      badge: 5, // Pending orders
      status: 'info',
      permission: 'view_orders'
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: UsersIcon,
      permission: 'view_customers'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartPieIcon,
      permission: 'view_analytics'
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: DocumentChartBarIcon,
      permission: 'view_reports'
    },
    {
      name: 'Users',
      href: '/users',
      icon: UsersIcon,
      permission: 'manage_users'
    },
    {
      name: 'Store Settings',
      href: '/store-settings',
      icon: BuildingStorefrontIcon,
      permission: 'manage_store_settings'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: CogIcon,
      permission: 'manage_settings'
    }
  ]



  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isItemActive = (item: NavigationItem): boolean => {
    if (location.pathname === item.href) return true
    if (item.children) {
      return item.children.some(child => location.pathname === child.href)
    }
    return false
  }

  const filteredNavigation = loading ? [] : navigation.filter(item =>
    !item.permission || (userRole && hasPermission(userRole, item.permission))
  ).map(item => ({
    ...item,
    children: item.children?.filter(child =>
      !child.permission || (userRole && hasPermission(userRole, child.permission))
    )
  }))

  return (
    <div className={clsx(
      'bg-admin-bg-secondary border-r border-admin-border transition-all duration-300 flex flex-col h-screen',
      collapsed ? 'w-16' : 'w-64'
    )}>
      {/* Logo and Toggle */}
      <div className="flex items-center justify-between h-16 px-4 border-b border-admin-border">
        {!collapsed && (
          <div className="flex items-center gap-2">
            {currentStore.settings?.logo ? (
              <img
                src={currentStore.settings.logo}
                alt={currentStore.name}
                className="h-8 w-8 rounded"
              />
            ) : (
              <div className="h-8 w-8 bg-admin-primary rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {currentStore.name.charAt(0)}
                </span>
              </div>
            )}
            <h1 className="text-lg font-serif font-bold text-admin-text-primary">
              {currentStore.name}
            </h1>
          </div>
        )}

        <button
          onClick={onToggleCollapse}
          className="p-1 text-admin-text-muted hover:text-admin-text-primary hover:bg-admin-hover rounded transition-colors"
          title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? (
            <ChevronRightIcon className="h-4 w-4" />
          ) : (
            <ChevronLeftIcon className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Store Info */}
      {!collapsed && (
        <div className="p-4 border-b border-admin-border">
          <div className="text-xs text-admin-text-muted uppercase tracking-wide mb-1">
            Store
          </div>
          <div className="text-sm font-medium text-admin-text-primary">
            {currentStore.name}
          </div>
          <div className="text-xs text-admin-text-muted">
            Role: {getUserRole()?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        {loading ? (
          <div className="space-y-2">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-10 bg-admin-hover rounded animate-pulse" />
            ))}
          </div>
        ) : error ? (
          <div className="p-4 text-center">
            <div className="text-admin-error text-sm mb-2">⚠️ Access Error</div>
            <div className="text-admin-text-muted text-xs">{error}</div>
          </div>
        ) : (
          filteredNavigation.map((item) => {
          const isActive = isItemActive(item)
          const isExpanded = expandedItems.includes(item.name)
          const hasChildren = item.children && item.children.length > 0

          return (
            <div key={item.name}>
              <div className="relative">
                <Link
                  to={item.href}
                  className={clsx(
                    'flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-all group',
                    isActive
                      ? 'bg-admin-primary text-white'
                      : 'text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover',
                    collapsed && 'justify-center'
                  )}
                  onClick={(e) => {
                    if (hasChildren && !collapsed) {
                      e.preventDefault()
                      toggleExpanded(item.name)
                    }
                  }}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  {!collapsed && (
                    <>
                      <span className="flex-1">{item.name}</span>
                      {item.badge && (
                        <span className={clsx(
                          'px-2 py-0.5 text-xs rounded-full',
                          item.status === 'warning' && 'bg-admin-warning text-white',
                          item.status === 'error' && 'bg-admin-error text-white',
                          item.status === 'info' && 'bg-admin-info text-white',
                          !item.status && 'bg-admin-text-muted text-white'
                        )}>
                          {item.badge}
                        </span>
                      )}
                      {hasChildren && (
                        <ChevronRightIcon className={clsx(
                          'h-4 w-4 transition-transform',
                          isExpanded && 'rotate-90'
                        )} />
                      )}
                    </>
                  )}
                </Link>

                {/* Status Indicator */}
                {collapsed && item.badge && (
                  <div className={clsx(
                    'absolute -top-1 -right-1 h-3 w-3 rounded-full',
                    item.status === 'warning' && 'bg-admin-warning',
                    item.status === 'error' && 'bg-admin-error',
                    item.status === 'info' && 'bg-admin-info',
                    !item.status && 'bg-admin-text-muted'
                  )} />
                )}
              </div>

              {/* Children */}
              {hasChildren && !collapsed && isExpanded && (
                <div className="ml-6 mt-1 space-y-1">
                  {item.children?.map((child) => (
                    <Link
                      key={child.name}
                      to={child.href}
                      className={clsx(
                        'flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-all',
                        location.pathname === child.href
                          ? 'bg-admin-primary/10 text-admin-primary'
                          : 'text-admin-text-muted hover:text-admin-text-primary hover:bg-admin-hover'
                      )}
                    >
                      <child.icon className="h-4 w-4" />
                      <span>{child.name}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          )
        }))}
      </nav>


    </div>
  )
}
