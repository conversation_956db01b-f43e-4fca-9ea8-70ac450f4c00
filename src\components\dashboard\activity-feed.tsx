import React from 'react'
import {
  ShoppingBagIcon,
  UserPlusIcon,
  CreditCardIcon,
  TruckIcon,
  StarIcon,
  ExclamationTriangleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline'

interface ActivityItem {
  id: string
  type: 'order' | 'customer' | 'payment' | 'shipping' | 'review' | 'alert'
  title: string
  description: string
  timestamp: string
  user?: string
  metadata?: Record<string, any>
}

// TODO: Implement real activity data from Supabase
const mockActivities: ActivityItem[] = []

const activityIcons = {
  order: ShoppingBagIcon,
  customer: UserPlusIcon,
  payment: CreditCardIcon,
  shipping: TruckIcon,
  review: StarIcon,
  alert: ExclamationTriangleIcon,
}

const activityColors = {
  order: 'text-admin-success bg-admin-success/10',
  customer: 'text-admin-info bg-admin-info/10',
  payment: 'text-admin-primary bg-admin-primary/10',
  shipping: 'text-admin-accent bg-admin-accent/10',
  review: 'text-yellow-500 bg-yellow-500/10',
  alert: 'text-admin-warning bg-admin-warning/10',
}

export function ActivityFeed() {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const handleActivityClick = (activity: ActivityItem) => {
    // TODO: Implement navigation to relevant page
  }

  return (
    <div className="admin-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
          Recent Activity
        </h3>
        <button className="text-sm text-admin-primary hover:text-admin-primary/80">
          View all
        </button>
      </div>

      <div className="space-y-4">
        {mockActivities.map((activity) => {
          const Icon = activityIcons[activity.type]
          const colorClass = activityColors[activity.type]

          return (
            <div
              key={activity.id}
              className="flex items-start gap-3 p-3 hover:bg-admin-hover rounded-lg transition-colors cursor-pointer"
              onClick={() => handleActivityClick(activity)}
            >
              <div className={`p-2 rounded-full ${colorClass}`}>
                <Icon className="h-4 w-4" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-admin-text-primary">
                    {activity.title}
                  </p>
                  <div className="flex items-center text-xs text-admin-text-muted">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    {activity.timestamp}
                  </div>
                </div>
                
                <p className="text-sm text-admin-text-secondary mt-1">
                  {activity.description}
                </p>
                
                {activity.user && (
                  <p className="text-xs text-admin-text-muted mt-1">
                    by {activity.user}
                  </p>
                )}
                
                {/* Activity-specific metadata */}
                {activity.metadata && (
                  <div className="mt-2 flex items-center gap-4 text-xs text-admin-text-muted">
                    {activity.metadata.amount && (
                      <span className="font-medium text-admin-text-primary">
                        {formatCurrency(activity.metadata.amount)}
                      </span>
                    )}
                    {activity.metadata.orderId && (
                      <span>Order #{activity.metadata.orderId}</span>
                    )}
                    {activity.metadata.rating && (
                      <div className="flex items-center">
                        {Array.from({ length: activity.metadata.rating }).map((_, i) => (
                          <StarIcon key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    )}
                    {activity.metadata.stock && (
                      <span className="text-admin-warning">
                        {activity.metadata.stock} left
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {mockActivities.length === 0 && (
        <div className="text-center py-8">
          <ClockIcon className="h-8 w-8 text-admin-text-muted mx-auto mb-2" />
          <p className="text-sm text-admin-text-muted">No recent activity</p>
          <p className="text-xs text-admin-text-muted mt-1">
            Activity will appear here as your store gets busy
          </p>
        </div>
      )}
    </div>
  )
}
