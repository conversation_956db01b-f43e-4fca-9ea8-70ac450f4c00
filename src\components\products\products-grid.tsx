import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  StarIcon,
  ExclamationTriangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'
import type { Product } from '@/types/product'

interface ProductsGridProps {
  products: Product[]
  isLoading: boolean
  error: any
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  onPageChange: (page: number) => void
  onRefresh: () => void
}

export function ProductsGrid({ 
  products, 
  isLoading, 
  error, 
  pagination,
  onPageChange,
  onRefresh 
}: ProductsGridProps) {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStatusBadge = (status: Product['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      draft: { color: 'bg-yellow-100 text-yellow-800', label: 'Draft' },
      archived: { color: 'bg-gray-100 text-gray-800', label: 'Archived' },
    }

    const config = statusConfig[status] || statusConfig.draft

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  const getStockStatus = (product: Product) => {
    if (product.inventory_quantity === 0) {
      return { color: 'text-red-600', text: 'Out of stock' }
    }
    
    if (product.inventory_quantity <= product.low_stock_threshold) {
      return { color: 'text-orange-600', text: 'Low stock' }
    }

    return { color: 'text-green-600', text: `${product.inventory_quantity} in stock` }
  }

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  const handleDelete = (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      console.log('Delete product:', productId)
      // TODO: Implement delete functionality
    }
  }

  const handleToggleFeatured = (productId: string, currentStatus: boolean) => {
    console.log('Toggle featured:', productId, !currentStatus)
    // TODO: Implement toggle featured functionality
  }

  if (error) {
    return (
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-8">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading products</p>
          <button
            onClick={onRefresh}
            className="px-4 py-2 bg-admin-primary text-white rounded-md hover:bg-admin-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="bg-admin-bg-secondary border border-admin-border rounded-lg overflow-hidden">
            <div className="animate-pulse">
              <div className="h-48 bg-admin-border"></div>
              <div className="p-4">
                <div className="h-4 bg-admin-border rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-admin-border rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-admin-border rounded w-1/3"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-12">
        <div className="text-center text-admin-text-secondary">
          <p className="text-lg mb-2">No products found</p>
          <p className="text-sm">Try adjusting your filters or create your first product</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => {
          const stockStatus = getStockStatus(product)
          
          return (
            <div
              key={product.id}
              className="bg-admin-bg-secondary border border-admin-border rounded-lg overflow-hidden hover:shadow-md transition-shadow group"
            >
              {/* Product Image */}
              <div className="relative h-48 bg-admin-border">
                {product.images && product.images.length > 0 ? (
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-admin-text-secondary">
                    <span className="text-sm">No image</span>
                  </div>
                )}

                {/* Selection Checkbox */}
                <div className="absolute top-2 left-2">
                  <input
                    type="checkbox"
                    checked={selectedProducts.includes(product.id)}
                    onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                    className="rounded border-admin-border text-admin-primary focus:ring-admin-primary bg-white"
                  />
                </div>

                {/* Featured Badge */}
                {product.is_featured && (
                  <div className="absolute top-2 right-2">
                    <StarSolidIcon className="h-5 w-5 text-yellow-400" />
                  </div>
                )}

                {/* Status Badge */}
                <div className="absolute bottom-2 left-2">
                  {getStatusBadge(product.status)}
                </div>

                {/* Actions Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Link
                    to={`/products/${product.id}`}
                    className="p-2 bg-white text-admin-text-primary rounded-md hover:bg-gray-100 transition-colors"
                    title="View product"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </Link>
                  
                  <Link
                    to={`/products/${product.id}/edit`}
                    className="p-2 bg-white text-admin-text-primary rounded-md hover:bg-gray-100 transition-colors"
                    title="Edit product"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Link>

                  <button
                    onClick={() => handleToggleFeatured(product.id, product.is_featured)}
                    className="p-2 bg-white rounded-md hover:bg-gray-100 transition-colors"
                    title={product.is_featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    <StarIcon className={`h-4 w-4 ${product.is_featured ? 'text-yellow-500' : 'text-gray-400'}`} />
                  </button>

                  <button
                    onClick={() => handleDelete(product.id)}
                    className="p-2 bg-white text-red-600 rounded-md hover:bg-gray-100 transition-colors"
                    title="Delete product"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                <div className="mb-2">
                  <h3 className="text-sm font-medium text-admin-text-primary line-clamp-2">
                    {product.name}
                  </h3>
                  <p className="text-xs text-admin-text-secondary mt-1">
                    SKU: {product.sku || 'N/A'}
                  </p>
                </div>

                {/* Jewelry Details */}
                {(product.metal_type || product.gemstone_type) && (
                  <div className="mb-2">
                    <p className="text-xs text-admin-text-secondary">
                      {product.metal_type}
                      {product.gemstone_type && ` • ${product.gemstone_type}`}
                    </p>
                  </div>
                )}

                {/* Price */}
                <div className="mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold text-admin-text-primary">
                      {formatCurrency(product.price)}
                    </span>
                    {product.compare_at_price && product.compare_at_price > product.price && (
                      <span className="text-sm text-admin-text-secondary line-through">
                        {formatCurrency(product.compare_at_price)}
                      </span>
                    )}
                  </div>
                </div>

                {/* Stock Status */}
                <div className="mb-2">
                  <p className={`text-sm ${stockStatus.color}`}>
                    {stockStatus.text}
                  </p>
                </div>

                {/* Category */}
                <div className="text-xs text-admin-text-secondary">
                  {product.category?.name || 'Uncategorized'}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Pagination */}
      {products.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-admin-text-secondary">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} results
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="p-2 text-admin-text-secondary hover:text-admin-text-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeftIcon className="h-4 w-4" />
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const page = i + 1
                return (
                  <button
                    key={page}
                    onClick={() => onPageChange(page)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${
                      page === pagination.page
                        ? 'bg-admin-primary text-white'
                        : 'text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover'
                    }`}
                  >
                    {page}
                  </button>
                )
              })}
            </div>

            <button
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
              className="p-2 text-admin-text-secondary hover:text-admin-text-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRightIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-admin-primary text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="flex items-center gap-4">
            <span className="text-sm">
              {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center gap-3">
              <button className="text-sm hover:underline">
                Delete Selected
              </button>
              <button className="text-sm hover:underline">
                Update Status
              </button>
              <button className="text-sm hover:underline">
                Export Selected
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
