-- 👥 User Management System Rebuild (Single Store Ownership)
-- Run this script in your Supabase SQL Editor

-- PHASE 1: Full Cleanup of Previous User Logic
-- =============================================

-- STEP 1: Drop old tables and policies
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS store_users CASCADE;
DROP TABLE IF EXISTS user_invites CASCADE;
DROP TABLE IF EXISTS user_activity_logs CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;

-- Drop all existing policies on auth.users if any
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'users' AND schemaname = 'auth'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON auth.users';
    END LOOP;
END $$;

-- PHASE 2: New User Management System (From Scratch)
-- ==================================================

-- STEP 2: Create admin_users table as specified
CREATE TABLE admin_users (
    id UUID PRIMARY KEY, -- Linked to Supabase Auth UID
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    password_hash TEXT NOT NULL, -- Managed via Supabase Auth
    role TEXT NOT NULL CHECK (role IN ('super_admin', 'admin', 'editor', 'viewer')),
    invited_by UUID REFERENCES admin_users(id), -- Nullable for Super Admin
    created_at TIMESTAMPTZ DEFAULT NOW(),
    store_id UUID NOT NULL -- Mandatory store binding
);

-- STEP 3: Create indexes for performance
CREATE INDEX idx_admin_users_email ON admin_users(email);
CREATE INDEX idx_admin_users_store_id ON admin_users(store_id);
CREATE INDEX idx_admin_users_role ON admin_users(role);

-- STEP 4: Enable RLS on admin_users table
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- STEP 5: Create RLS policies for admin_users
-- Policy 1: Users can view their own record and admins can view all
CREATE POLICY "admin_users_select_policy" ON admin_users
    FOR SELECT USING (
        store_id = '550e8400-e29b-41d4-a716-************' AND
        (auth.uid() = id OR 
         EXISTS (
             SELECT 1 FROM admin_users au 
             WHERE au.id = auth.uid() 
             AND au.role IN ('admin', 'super_admin')
             AND au.store_id = '550e8400-e29b-41d4-a716-************'
         ))
    );

-- Policy 2: Only super_admin and admin can insert new users
CREATE POLICY "admin_users_insert_policy" ON admin_users
    FOR INSERT WITH CHECK (
        store_id = '550e8400-e29b-41d4-a716-************' AND
        EXISTS (
            SELECT 1 FROM admin_users au 
            WHERE au.id = auth.uid() 
            AND au.role IN ('admin', 'super_admin')
            AND au.store_id = '550e8400-e29b-41d4-a716-************'
        )
    );

-- Policy 3: Only super_admin and admin can update users (except super_admin cannot be updated by others)
CREATE POLICY "admin_users_update_policy" ON admin_users
    FOR UPDATE USING (
        store_id = '550e8400-e29b-41d4-a716-************' AND
        (auth.uid() = id OR 
         (EXISTS (
             SELECT 1 FROM admin_users au 
             WHERE au.id = auth.uid() 
             AND au.role IN ('admin', 'super_admin')
             AND au.store_id = '550e8400-e29b-41d4-a716-************'
         ) AND role != 'super_admin'))
    );

-- Policy 4: Only super_admin and admin can delete users (except super_admin cannot be deleted)
CREATE POLICY "admin_users_delete_policy" ON admin_users
    FOR DELETE USING (
        store_id = '550e8400-e29b-41d4-a716-************' AND
        role != 'super_admin' AND
        EXISTS (
            SELECT 1 FROM admin_users au 
            WHERE au.id = auth.uid() 
            AND au.role IN ('admin', 'super_admin')
            AND au.store_id = '550e8400-e29b-41d4-a716-************'
        )
    );

-- STEP 6: Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at column and trigger
ALTER TABLE admin_users ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();

CREATE TRIGGER update_admin_users_updated_at
    BEFORE UPDATE ON admin_users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- STEP 7: Seed the Super Admin (Store Owner)
-- This creates the first and only Super Admin tied to the Womanza store
INSERT INTO admin_users (
    id,
    email,
    full_name,
    password_hash,
    role,
    invited_by,
    store_id
) VALUES (
    '00000000-0000-0000-0000-000000000001', -- Hardcoded Super Admin ID
    '<EMAIL>',
    'Womanza Store Owner',
    'supabase_auth_managed', -- Placeholder - actual password managed by Supabase Auth
    'super_admin',
    NULL, -- Super Admin is not invited by anyone
    '550e8400-e29b-41d4-a716-************' -- Womanza Store ID
) ON CONFLICT (email) DO UPDATE SET
    role = 'super_admin',
    store_id = '550e8400-e29b-41d4-a716-************';

-- STEP 8: Create function to create new admin users
CREATE OR REPLACE FUNCTION create_admin_user(
    p_email TEXT,
    p_full_name TEXT,
    p_password TEXT,
    p_role TEXT,
    p_invited_by UUID
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
    auth_user_data JSON;
BEGIN
    -- Validate role
    IF p_role NOT IN ('admin', 'editor', 'viewer') THEN
        RETURN json_build_object('success', false, 'error', 'Invalid role specified');
    END IF;
    
    -- Check if inviter has permission
    IF NOT EXISTS (
        SELECT 1 FROM admin_users 
        WHERE id = p_invited_by 
        AND role IN ('super_admin', 'admin')
        AND store_id = '550e8400-e29b-41d4-a716-************'
    ) THEN
        RETURN json_build_object('success', false, 'error', 'Insufficient permissions to create user');
    END IF;
    
    -- Generate new UUID for user
    new_user_id := gen_random_uuid();
    
    -- Insert into admin_users table
    INSERT INTO admin_users (
        id,
        email,
        full_name,
        password_hash,
        role,
        invited_by,
        store_id
    ) VALUES (
        new_user_id,
        p_email,
        p_full_name,
        'supabase_auth_managed',
        p_role,
        p_invited_by,
        '550e8400-e29b-41d4-a716-************'
    );
    
    RETURN json_build_object(
        'success', true, 
        'user_id', new_user_id,
        'message', 'User created successfully'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object('success', false, 'error', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 9: Create function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM admin_users
    WHERE id = user_id
    AND store_id = '550e8400-e29b-41d4-a716-************';
    
    RETURN COALESCE(user_role, 'none');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 10: Success message
SELECT '✅ User Management System Rebuild Complete!' as status;
SELECT 'Super Admin seeded: <EMAIL>' as super_admin;
SELECT 'Store ID: 550e8400-e29b-41d4-a716-************' as store_id;
