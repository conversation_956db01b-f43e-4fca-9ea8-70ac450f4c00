-- 🔧 COMPREHENSIVE FIX - Sidebar, User Data & Console Errors
-- Run this script in your Supabase SQL Editor

-- STEP 1: Clean up all existing policies to prevent conflicts
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    -- Drop all policies on store_users
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'store_users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON store_users';
    END LOOP;
    
    -- Drop all policies on users
    FOR policy_name IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON users';
    END LOOP;
    
    RAISE NOTICE 'All existing policies dropped';
END $$;

-- STEP 2: Create simple, working policies
-- For store_users: Allow all operations for authenticated users
CREATE POLICY "allow_authenticated_store_users" ON store_users
    FOR ALL USING (auth.role() = 'authenticated');

-- For users: Allow all operations for authenticated users  
CREATE POLICY "allow_authenticated_users" ON users
    FOR ALL USING (auth.role() = 'authenticated');

-- STEP 3: Fix user data issues
DO $$
DECLARE
    admin_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Find the admin user by email in auth.users
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    IF admin_user_id IS NOT NULL THEN
        -- Ensure user exists in users table with correct details
        INSERT INTO users (id, email, first_name, last_name, password_hash, created_at, updated_at)
        VALUES (
            admin_user_id,
            '<EMAIL>',
            'Usman',
            'Ali',
            'auth_user',
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            updated_at = NOW();
        
        -- Ensure user exists in store_users table with admin role
        INSERT INTO store_users (user_id, store_id, role, created_at, updated_at)
        VALUES (
            admin_user_id,
            womanza_store_id,
            'admin',
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id, store_id) DO UPDATE SET
            role = 'admin',
            updated_at = NOW();
        
        RAISE NOTICE 'Fixed user data for Usman Ali (admin role)';
    ELSE
        RAISE NOTICE 'Admin user not found with email: <EMAIL>';
    END IF;
END $$;

-- STEP 4: Verify the fix
SELECT 'Current auth users:' as info;
SELECT id, email, created_at FROM auth.users ORDER BY created_at;

SELECT 'Current store users:' as info;
SELECT su.id, su.user_id, su.role, au.email 
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY su.created_at;

SELECT 'Current users table:' as info;
SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at;

SELECT '✅ Comprehensive fix completed!' as status;
