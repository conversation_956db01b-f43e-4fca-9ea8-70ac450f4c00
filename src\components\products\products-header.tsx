import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { 
  PlusIcon, 
  ArrowPathIcon,
  EllipsisVerticalIcon,
  DocumentArrowDownIcon,
  DocumentArrowUpIcon,
  FolderIcon
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'

interface ProductsHeaderProps {
  onRefresh: () => void
  totalProducts: number
}

export function ProductsHeader({ onRefresh, totalProducts }: ProductsHeaderProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await onRefresh()
    setTimeout(() => setIsRefreshing(false), 500)
  }

  const handleExport = () => {
    console.log('Export products')
    // TODO: Implement export functionality
  }

  const handleImport = () => {
    console.log('Import products')
    // TODO: Implement import functionality
  }

  const handleBulkActions = () => {
    console.log('Bulk actions')
    // TODO: Implement bulk actions
  }

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-admin-text-primary">Products</h1>
        <p className="mt-1 text-sm text-admin-text-secondary">
          Manage your product catalog and inventory
          {totalProducts > 0 && (
            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-admin-primary/10 text-admin-primary">
              {totalProducts.toLocaleString()} products
            </span>
          )}
        </p>
      </div>

      <div className="flex items-center gap-3">
        {/* Refresh Button */}
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary hover:text-admin-text-primary border border-admin-border rounded-md hover:bg-admin-hover transition-colors disabled:opacity-50"
          title="Refresh products"
        >
          <ArrowPathIcon className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>

        {/* More Actions Menu */}
        <Menu as="div" className="relative">
          <Menu.Button className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary hover:text-admin-text-primary border border-admin-border rounded-md hover:bg-admin-hover transition-colors">
            <EllipsisVerticalIcon className="h-4 w-4" />
            More
          </Menu.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right bg-admin-bg-primary border border-admin-border rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="py-1">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleExport}
                      className={`${
                        active ? 'bg-admin-hover' : ''
                      } flex items-center gap-3 w-full px-4 py-2 text-sm text-admin-text-primary`}
                    >
                      <DocumentArrowDownIcon className="h-4 w-4" />
                      Export Products
                    </button>
                  )}
                </Menu.Item>

                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleImport}
                      className={`${
                        active ? 'bg-admin-hover' : ''
                      } flex items-center gap-3 w-full px-4 py-2 text-sm text-admin-text-primary`}
                    >
                      <DocumentArrowUpIcon className="h-4 w-4" />
                      Import Products
                    </button>
                  )}
                </Menu.Item>

                <Menu.Item>
                  {({ active }) => (
                    <Link
                      to="/products/categories"
                      className={`${
                        active ? 'bg-admin-hover' : ''
                      } flex items-center gap-3 w-full px-4 py-2 text-sm text-admin-text-primary`}
                    >
                      <FolderIcon className="h-4 w-4" />
                      Manage Categories
                    </Link>
                  )}
                </Menu.Item>

                <div className="border-t border-admin-border my-1" />

                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={handleBulkActions}
                      className={`${
                        active ? 'bg-admin-hover' : ''
                      } flex items-center gap-3 w-full px-4 py-2 text-sm text-admin-text-primary`}
                    >
                      Bulk Actions
                    </button>
                  )}
                </Menu.Item>
              </div>
            </Menu.Items>
          </Transition>
        </Menu>


      </div>
    </div>
  )
}
