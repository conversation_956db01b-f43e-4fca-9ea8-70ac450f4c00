-- 🔧 Fix Foreign Key Issues - Run this FIRST
-- This script removes problematic foreign key constraints and policies

-- STEP 1: Drop ALL policies on both users and store_users tables
DROP POLICY IF EXISTS "Store users can view users in their store" ON users;
DROP POLICY IF EXISTS "Store admins can create users" ON users;

DROP POLICY IF EXISTS "user_view_own" ON store_users;
DROP POLICY IF EXISTS "Users can view own store users" ON store_users;
DROP POLICY IF EXISTS "Store users can view store users" ON store_users;
DROP POLICY IF EXISTS "Store admins can manage store users" ON store_users;
DROP POLICY IF EXISTS "admin_update" ON store_users;
DROP POLICY IF EXISTS "admin_insert" ON store_users;
DROP POLICY IF EXISTS "admin_delete" ON store_users;
DROP POLICY IF EXISTS "user_select" ON store_users;
DROP POLICY IF EXISTS "user_insert" ON store_users;
DROP POLICY IF EXISTS "user_update" ON store_users;
DROP POLICY IF EXISTS "user_delete" ON store_users;

-- Drop any remaining policies programmatically
DO $$
DECLARE
    policy_name TEXT;
BEGIN
    -- Drop all policies on users table
    FOR policy_name IN
        SELECT policyname
        FROM pg_policies
        WHERE tablename = 'users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON users';
    END LOOP;

    -- Drop all policies on store_users table
    FOR policy_name IN
        SELECT policyname
        FROM pg_policies
        WHERE tablename = 'store_users'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_name || '" ON store_users';
    END LOOP;
END $$;

-- STEP 2: Remove all foreign key constraints from store_users table
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS fk_store_users_user_id;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey1;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey2;

-- STEP 3: Drop users table completely to avoid conflicts
DROP TABLE IF EXISTS users CASCADE;

-- STEP 4: Make sure user_id column is just UUID without constraints
ALTER TABLE store_users ALTER COLUMN user_id TYPE UUID;

-- Check if there are any other foreign key constraints and remove them
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    FOR constraint_name IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'store_users'::regclass 
        AND contype = 'f'
    LOOP
        EXECUTE 'ALTER TABLE store_users DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
END $$;

-- STEP 5: Recreate essential policies for store_users
CREATE POLICY "Users can view own store users" ON store_users
    FOR SELECT USING (
        user_id = auth.uid() OR
        store_id IN (
            SELECT store_id
            FROM store_users
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Store admins can manage store users" ON store_users
    FOR ALL USING (
        EXISTS (
            SELECT 1
            FROM store_users su
            WHERE su.user_id = auth.uid()
            AND su.store_id = store_users.store_id
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Success message
SELECT 'Foreign key constraints and policies fixed successfully!' as status;
