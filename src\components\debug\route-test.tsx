import { Link, useLocation } from '@tanstack/react-router'

export function RouteTest() {
  const location = useLocation()
  
  return (
    <div className="p-4 bg-yellow-100 border border-yellow-300 rounded-md">
      <h3 className="font-bold text-yellow-800">Route Debug Info</h3>
      <p className="text-yellow-700">Current path: {location.pathname}</p>
      <div className="mt-2 space-x-2">
        <Link 
          to="/products" 
          className="px-2 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Products
        </Link>
        <Link 
          to="/products/new" 
          className="px-2 py-1 bg-green-500 text-white rounded text-sm"
        >
          New Product
        </Link>
        <Link 
          to="/products/categories" 
          className="px-2 py-1 bg-purple-500 text-white rounded text-sm"
        >
          Categories
        </Link>
        <Link 
          to="/products/inventory" 
          className="px-2 py-1 bg-orange-500 text-white rounded text-sm"
        >
          Inventory
        </Link>
      </div>
    </div>
  )
}
