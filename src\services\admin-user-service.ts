import { supabase } from '@/lib/supabase'

// Store configuration - hardcoded as per specification
const WOMANZA_STORE_ID = '550e8400-e29b-41d4-a716-446655440001'
const SUPER_ADMIN_ID = '00000000-0000-0000-0000-000000000001'

export type AdminRole = 'super_admin' | 'admin' | 'editor' | 'viewer'

export interface AdminUser {
  id: string
  email: string
  full_name: string
  role: AdminRole
  invited_by: string | null
  created_at: string
  updated_at: string
  store_id: string
}

export interface CreateAdminUserData {
  email: string
  fullName: string
  password: string
  role: AdminRole
}

export interface AdminUserResponse {
  success: boolean
  data?: any
  error?: string
}

export class AdminUserService {
  /**
   * Get all admin users for the store
   */
  static async getAllAdminUsers(): Promise<AdminUser[]> {
    try {
      const { data, error } = await supabase
        .from('admin_users')
        .select('*')
        .eq('store_id', WOMANZA_STORE_ID)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error fetching admin users:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getAllAdminUsers:', error)
      return []
    }
  }

  /**
   * Get current user's role
   */
  static async getCurrentUserRole(): Promise<AdminRole | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      const { data, error } = await supabase
        .from('admin_users')
        .select('role')
        .eq('id', user.id)
        .eq('store_id', WOMANZA_STORE_ID)
        .single()

      if (error) {
        console.error('Error fetching user role:', error)
        return null
      }

      return data?.role || null
    } catch (error) {
      console.error('Error in getCurrentUserRole:', error)
      return null
    }
  }

  /**
   * Check if current user can manage users
   */
  static async canManageUsers(): Promise<boolean> {
    const role = await this.getCurrentUserRole()
    return role === 'super_admin' || role === 'admin'
  }

  /**
   * Create a new admin user
   */
  static async createAdminUser(
    userData: CreateAdminUserData
  ): Promise<AdminUserResponse> {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (!currentUser) {
        return {
          success: false,
          error: 'Not authenticated'
        }
      }

      // Validate input
      if (!userData.email || !userData.fullName || !userData.password || !userData.role) {
        return {
          success: false,
          error: 'All fields are required'
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        return {
          success: false,
          error: 'Invalid email format'
        }
      }

      // Validate password strength
      if (userData.password.length < 6) {
        return {
          success: false,
          error: 'Password must be at least 6 characters long'
        }
      }

      // Validate role
      if (!['admin', 'editor', 'viewer'].includes(userData.role)) {
        return {
          success: false,
          error: 'Invalid role specified'
        }
      }

      // Check if current user can create this role
      const currentUserRole = await this.getCurrentUserRole()
      if (!currentUserRole || !['super_admin', 'admin'].includes(currentUserRole)) {
        return {
          success: false,
          error: 'You do not have permission to create users'
        }
      }

      // Check role limits
      if (userData.role === 'super_admin') {
        return {
          success: false,
          error: 'Cannot create super admin users'
        }
      }

      // Check if admin role limit is reached (only one admin allowed)
      if (userData.role === 'admin') {
        const existingAdmins = await this.getAllAdminUsers()
        const adminCount = existingAdmins.filter(u => u.role === 'admin').length
        if (adminCount >= 1) {
          return {
            success: false,
            error: 'Only one admin user is allowed per store'
          }
        }
      }

      // Create user in Supabase Auth first
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true
      })

      if (authError) {
        console.error('Auth error:', authError)
        return {
          success: false,
          error: authError.message || 'Failed to create user authentication'
        }
      }

      if (!authData.user) {
        return {
          success: false,
          error: 'Failed to create user authentication'
        }
      }

      // Insert into admin_users table
      const { data: adminUserData, error: adminUserError } = await supabase
        .from('admin_users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.fullName,
          password_hash: 'supabase_auth_managed',
          role: userData.role,
          invited_by: currentUser.id,
          store_id: WOMANZA_STORE_ID
        })
        .select()
        .single()

      if (adminUserError) {
        console.error('Admin user creation error:', adminUserError)
        
        // Cleanup: delete the auth user if admin_users insertion failed
        await supabase.auth.admin.deleteUser(authData.user.id)
        
        return {
          success: false,
          error: adminUserError.message || 'Failed to create admin user record'
        }
      }

      return {
        success: true,
        data: adminUserData
      }
    } catch (error) {
      console.error('Error creating admin user:', error)
      return {
        success: false,
        error: 'An unexpected error occurred'
      }
    }
  }

  /**
   * Update admin user
   */
  static async updateAdminUser(
    userId: string,
    updateData: Partial<Pick<AdminUser, 'full_name' | 'role'>>
  ): Promise<AdminUserResponse> {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (!currentUser) {
        return {
          success: false,
          error: 'Not authenticated'
        }
      }

      // Check permissions
      const currentUserRole = await this.getCurrentUserRole()
      if (!currentUserRole || !['super_admin', 'admin'].includes(currentUserRole)) {
        return {
          success: false,
          error: 'You do not have permission to update users'
        }
      }

      // Cannot update super admin
      const { data: targetUser } = await supabase
        .from('admin_users')
        .select('role')
        .eq('id', userId)
        .eq('store_id', WOMANZA_STORE_ID)
        .single()

      if (targetUser?.role === 'super_admin') {
        return {
          success: false,
          error: 'Cannot update super admin user'
        }
      }

      // Update the user
      const { data, error } = await supabase
        .from('admin_users')
        .update(updateData)
        .eq('id', userId)
        .eq('store_id', WOMANZA_STORE_ID)
        .select()
        .single()

      if (error) {
        console.error('Error updating admin user:', error)
        return {
          success: false,
          error: error.message || 'Failed to update user'
        }
      }

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('Error in updateAdminUser:', error)
      return {
        success: false,
        error: 'An unexpected error occurred'
      }
    }
  }

  /**
   * Delete admin user
   */
  static async deleteAdminUser(userId: string): Promise<AdminUserResponse> {
    try {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      if (!currentUser) {
        return {
          success: false,
          error: 'Not authenticated'
        }
      }

      // Check permissions
      const currentUserRole = await this.getCurrentUserRole()
      if (!currentUserRole || !['super_admin', 'admin'].includes(currentUserRole)) {
        return {
          success: false,
          error: 'You do not have permission to delete users'
        }
      }

      // Cannot delete super admin or self
      if (userId === SUPER_ADMIN_ID || userId === currentUser.id) {
        return {
          success: false,
          error: 'Cannot delete super admin or your own account'
        }
      }

      // Get user to check role
      const { data: targetUser } = await supabase
        .from('admin_users')
        .select('role')
        .eq('id', userId)
        .eq('store_id', WOMANZA_STORE_ID)
        .single()

      if (targetUser?.role === 'super_admin') {
        return {
          success: false,
          error: 'Cannot delete super admin user'
        }
      }

      // Delete from admin_users table
      const { error: adminUserError } = await supabase
        .from('admin_users')
        .delete()
        .eq('id', userId)
        .eq('store_id', WOMANZA_STORE_ID)

      if (adminUserError) {
        console.error('Error deleting admin user:', adminUserError)
        return {
          success: false,
          error: 'Failed to delete user'
        }
      }

      // Delete from Supabase Auth
      const { error: authError } = await supabase.auth.admin.deleteUser(userId)
      if (authError) {
        console.error('Error deleting auth user:', authError)
        // Note: admin_users record is already deleted, but auth user remains
        // This is acceptable as the user won't be able to access the system
      }

      return {
        success: true,
        data: { message: 'User deleted successfully' }
      }
    } catch (error) {
      console.error('Error in deleteAdminUser:', error)
      return {
        success: false,
        error: 'An unexpected error occurred'
      }
    }
  }

  /**
   * Check if user can perform action on target user
   */
  static canPerformAction(
    currentUserRole: AdminRole,
    targetUserRole: AdminRole,
    action: 'view' | 'edit' | 'delete'
  ): boolean {
    // Super admin can do everything except delete themselves
    if (currentUserRole === 'super_admin') {
      return true
    }

    // Admin can manage editor and viewer, but not super_admin
    if (currentUserRole === 'admin') {
      return targetUserRole !== 'super_admin'
    }

    // Editor and viewer can only view
    return action === 'view'
  }

  /**
   * Get role display name
   */
  static getRoleDisplayName(role: AdminRole): string {
    const roleNames = {
      super_admin: 'Super Admin',
      admin: 'Admin',
      editor: 'Editor',
      viewer: 'Viewer'
    }
    return roleNames[role] || role
  }

  /**
   * Get role description
   */
  static getRoleDescription(role: AdminRole): string {
    const descriptions = {
      super_admin: 'Full control — owns store and user base',
      admin: 'Full admin access, cannot override Super Admin',
      editor: 'Product/category/order management only',
      viewer: 'View-only access to analytics and listings'
    }
    return descriptions[role] || ''
  }
}
