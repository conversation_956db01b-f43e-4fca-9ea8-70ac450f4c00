import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { useStore } from '@/contexts/store-context'
import { ProductService } from '@/services/product-service'
import { 
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  PencilIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { Link } from '@tanstack/react-router'
import type { Product } from '@/types/product'

export function InventoryContent() {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [editingStock, setEditingStock] = useState<{ productId: string; quantity: number } | null>(null)

  // Fetch products with inventory focus
  const { data: productsData, isLoading, refetch } = useQuery({
    queryKey: ['products-inventory', currentStore?.id, searchTerm],
    queryFn: () => ProductService.getProducts(currentStore!.id, {
      search: searchTerm,
      sort_by: 'inventory_quantity',
      sort_order: 'asc',
      limit: 100
    }),
    enabled: !!currentStore?.id,
  })

  // Update stock mutation
  const updateStockMutation = useMutation({
    mutationFn: ({ productId, quantity }: { productId: string; quantity: number }) =>
      ProductService.updateProduct(productId, { inventory_quantity: quantity }),
    onSuccess: () => {
      toast.success('Stock updated successfully!')
      queryClient.invalidateQueries({ queryKey: ['products-inventory'] })
      setEditingStock(null)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update stock')
    },
  })

  const handleStockUpdate = (productId: string, newQuantity: number) => {
    if (newQuantity < 0) {
      toast.error('Stock quantity cannot be negative')
      return
    }
    updateStockMutation.mutate({ productId, quantity: newQuantity })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStockStatus = (product: Product) => {
    if (!product.track_inventory) {
      return { color: 'text-gray-500', text: 'Not tracked', bgColor: 'bg-gray-100' }
    }
    if (product.inventory_quantity === 0) {
      return { color: 'text-red-600', text: 'Out of stock', bgColor: 'bg-red-50' }
    }
    if (product.inventory_quantity <= product.low_stock_threshold) {
      return { color: 'text-orange-600', text: 'Low stock', bgColor: 'bg-orange-50' }
    }
    return { color: 'text-green-600', text: 'In stock', bgColor: 'bg-green-50' }
  }

  const products = productsData?.data || []
  const lowStockProducts = products.filter(p => 
    p.track_inventory && p.inventory_quantity <= p.low_stock_threshold
  )
  const outOfStockProducts = products.filter(p => 
    p.track_inventory && p.inventory_quantity === 0
  )
  const totalValue = products.reduce((sum, p) => sum + (p.price * p.inventory_quantity), 0)

  if (!currentStore) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-admin-text-secondary">Please select a store to manage inventory</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            to="/products"
            className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
            title="Back to products"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          
          <div>
            <h1 className="text-2xl font-semibold text-admin-text-primary">Inventory</h1>
            <p className="mt-1 text-sm text-admin-text-secondary">
              Monitor and manage your product stock levels
            </p>
          </div>
        </div>

        <button
          onClick={() => refetch()}
          className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary hover:text-admin-text-primary border border-admin-border rounded-md hover:bg-admin-hover transition-colors"
        >
          <ArrowPathIcon className="h-4 w-4" />
          Refresh
        </button>
      </div>

      {/* Inventory Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-admin-text-secondary">Total Products</p>
              <p className="text-2xl font-semibold text-admin-text-primary">
                {products.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-admin-text-secondary">Low Stock</p>
              <p className="text-2xl font-semibold text-orange-600">
                {lowStockProducts.length}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-orange-400" />
          </div>
        </div>

        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-admin-text-secondary">Out of Stock</p>
              <p className="text-2xl font-semibold text-red-600">
                {outOfStockProducts.length}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-admin-text-secondary">Total Value</p>
              <p className="text-2xl font-semibold text-admin-text-primary">
                {formatCurrency(totalValue)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-admin-text-secondary" />
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search products..."
          className="w-full pl-10 pr-4 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
        />
      </div>

      {/* Inventory Table */}
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <p className="text-admin-text-secondary">Loading inventory...</p>
          </div>
        ) : products.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-admin-text-secondary">No products found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    SKU
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Current Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Value
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-admin-bg-secondary divide-y divide-admin-border">
                {products.map((product) => {
                  const stockStatus = getStockStatus(product)
                  const isEditing = editingStock?.productId === product.id
                  
                  return (
                    <tr key={product.id} className={`hover:bg-admin-hover ${stockStatus.bgColor}`}>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {product.images && product.images.length > 0 ? (
                              <img
                                className="h-10 w-10 rounded-lg object-cover"
                                src={product.images[0]}
                                alt={product.name}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-lg bg-admin-border flex items-center justify-center">
                                <span className="text-admin-text-secondary text-xs">No img</span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-admin-text-primary">
                              {product.name}
                            </div>
                            <div className="text-sm text-admin-text-secondary">
                              {formatCurrency(product.price)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-admin-text-primary">
                          {product.sku || 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {isEditing ? (
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              min="0"
                              value={editingStock.quantity}
                              onChange={(e) => setEditingStock({
                                ...editingStock,
                                quantity: parseInt(e.target.value) || 0
                              })}
                              className="w-20 px-2 py-1 text-sm border border-admin-border rounded bg-admin-bg-primary"
                              autoFocus
                            />
                            <button
                              onClick={() => handleStockUpdate(product.id, editingStock.quantity)}
                              className="px-2 py-1 text-xs bg-admin-primary text-white rounded hover:bg-admin-primary/90"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => setEditingStock(null)}
                              className="px-2 py-1 text-xs text-admin-text-secondary hover:text-admin-text-primary"
                            >
                              Cancel
                            </button>
                          </div>
                        ) : (
                          <div className="text-sm text-admin-text-primary">
                            {product.track_inventory ? product.inventory_quantity : 'Not tracked'}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <span className={`text-sm font-medium ${stockStatus.color}`}>
                          {stockStatus.text}
                        </span>
                        {product.track_inventory && product.inventory_quantity <= product.low_stock_threshold && (
                          <div className="text-xs text-admin-text-secondary">
                            Threshold: {product.low_stock_threshold}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-admin-text-primary">
                          {formatCurrency(product.price * product.inventory_quantity)}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-right">
                        {product.track_inventory && !isEditing && (
                          <button
                            onClick={() => setEditingStock({
                              productId: product.id,
                              quantity: product.inventory_quantity
                            })}
                            className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
                            title="Edit stock"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
