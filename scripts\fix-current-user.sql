-- 🔧 Fix Current User Access
-- Run this AFTER the main migration script

-- STEP 1: Check current auth user
SELECT 'Current auth user:' as info;
SELECT id, email, created_at FROM auth.users ORDER BY created_at;

-- STEP 2: Update or insert current user into admin_users
-- Replace the UUID below with your actual current user ID from the error logs
DO $$
DECLARE
    current_user_id UUID := 'a9c28287-fff6-4ac4-b4a8-5e0b8a4f9315'; -- Your current user ID
    current_email TEXT;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Get current user email from auth.users
    SELECT email INTO current_email
    FROM auth.users 
    WHERE id = current_user_id;
    
    IF current_email IS NOT NULL THEN
        -- Insert or update the current user as super admin
        INSERT INTO admin_users (
            id,
            email,
            full_name,
            password_hash,
            role,
            invited_by,
            store_id
        ) VALUES (
            current_user_id,
            current_email,
            CASE 
                WHEN current_email = '<EMAIL>' THEN 'Womanza Store Owner'
                WHEN current_email = '<EMAIL>' THEN 'Usman Ali'
                ELSE 'Admin User'
            END,
            'supabase_auth_managed',
            CASE 
                WHEN current_email = '<EMAIL>' THEN 'super_admin'
                ELSE 'admin'
            END,
            NULL,
            womanza_store_id
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            full_name = EXCLUDED.full_name,
            role = EXCLUDED.role,
            store_id = EXCLUDED.store_id,
            updated_at = NOW();
        
        RAISE NOTICE 'User % added/updated as admin', current_email;
    ELSE
        RAISE NOTICE 'User ID % not found in auth.users', current_user_id;
    END IF;
END $$;

-- STEP 3: Verify the fix
SELECT 'Current admin users:' as info;
SELECT 
    id,
    email,
    full_name,
    role,
    store_id,
    created_at
FROM admin_users 
ORDER BY created_at;

-- STEP 4: Test access
SELECT 'Testing current user access...' as info;
SELECT get_user_role('a9c28287-fff6-4ac4-b4a8-5e0b8a4f9315'::uuid) as current_user_role;

SELECT '✅ Current user access fixed!' as status;
