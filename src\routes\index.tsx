import React, { useEffect } from 'react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useAuth } from '@/contexts/auth-context'

export const Route = createFileRoute('/')({
  component: Index,
})

function Index() {
  const { user, loading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to login using proper router navigation
        navigate({ to: '/login' })
      } else {
        // Redirect to dashboard using proper router navigation
        navigate({ to: '/dashboard' })
      }
    }
  }, [user, loading, navigate])

  // Show loading state while determining where to redirect
  return (
    <div className="min-h-screen flex items-center justify-center bg-admin-bg-primary">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-admin-primary mx-auto mb-4"></div>
        <p className="text-admin-text-secondary">
          {loading ? 'Loading...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  )
}
