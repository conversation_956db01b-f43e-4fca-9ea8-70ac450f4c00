import React from 'react'
import { PhotoIcon, ArrowUpTrayIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline'

interface ProductForm {
  name: string
  description: string
  short_description: string
  sku: string
  price: number
  compare_at_price: number
  cost_price: number
  inventory_quantity: number
  low_stock_threshold: number
  track_inventory: boolean
  status: 'active' | 'draft' | 'archived'
  is_featured: boolean
  weight_grams: number
  category_id: string
  images: string[]
  seo_title: string
  seo_description: string
  metal_type: string
  gemstone_type: string
  gemstone_carat: number
  metal_purity: string
}

interface Category {
  id: string
  name: string
}

interface AddProductTabsProps {
  activeTab: string
  form: ProductForm
  categories: Category[]
  uploadingImages: boolean
  handleInputChange: (field: keyof ProductForm, value: any) => void
  handleImageUpload: (files: FileList | null) => void
  removeImage: (index: number) => void
  generateSKU: () => string
}

export function AddProductTabs({
  activeTab,
  form,
  categories,
  uploadingImages,
  handleInputChange,
  handleImageUpload,
  removeImage,
  generateSKU
}: AddProductTabsProps) {
  
  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Product Name *
          </label>
          <input
            type="text"
            value={form.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="Enter product name"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            SKU
          </label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <input
              type="text"
              value={form.sku}
              onChange={(e) => handleInputChange('sku', e.target.value)}
              className="flex-1 block w-full border border-gray-300 dark:border-gray-600 rounded-l-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="Product SKU"
            />
            <button
              type="button"
              onClick={() => handleInputChange('sku', generateSKU())}
              className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-300 text-sm"
            >
              Generate
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Category
          </label>
          <select
            value={form.category_id}
            onChange={(e) => handleInputChange('category_id', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Short Description
          </label>
          <textarea
            value={form.short_description}
            onChange={(e) => handleInputChange('short_description', e.target.value)}
            rows={2}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="Brief description for product listings"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Description
          </label>
          <textarea
            value={form.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={6}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="Detailed product description"
          />
        </div>
      </div>
    </div>
  )

  const renderPricing = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Pricing</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Price *
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">$</span>
            </div>
            <input
              type="number"
              step="0.01"
              min="0"
              value={form.price}
              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
              className="block w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="0.00"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Compare at Price
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">$</span>
            </div>
            <input
              type="number"
              step="0.01"
              min="0"
              value={form.compare_at_price}
              onChange={(e) => handleInputChange('compare_at_price', parseFloat(e.target.value) || 0)}
              className="block w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="0.00"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Cost Price
          </label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">$</span>
            </div>
            <input
              type="number"
              step="0.01"
              min="0"
              value={form.cost_price}
              onChange={(e) => handleInputChange('cost_price', parseFloat(e.target.value) || 0)}
              className="block w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="0.00"
            />
          </div>
        </div>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">💡 Pricing Tips</h4>
        <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
          <li>• <strong>Price:</strong> The selling price customers will pay</li>
          <li>• <strong>Compare at Price:</strong> Original price to show discounts</li>
          <li>• <strong>Cost Price:</strong> Your cost (for profit calculations)</li>
        </ul>
      </div>
    </div>
  )

  const renderInventory = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Inventory</h3>
      </div>
      
      <div className="space-y-6">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="track_inventory"
            checked={form.track_inventory}
            onChange={(e) => handleInputChange('track_inventory', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="track_inventory" className="ml-2 block text-sm text-gray-900 dark:text-white">
            Track inventory for this product
          </label>
        </div>

        {form.track_inventory && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Quantity in Stock
              </label>
              <input
                type="number"
                min="0"
                value={form.inventory_quantity}
                onChange={(e) => handleInputChange('inventory_quantity', parseInt(e.target.value) || 0)}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Low Stock Threshold
              </label>
              <input
                type="number"
                min="0"
                value={form.low_stock_threshold}
                onChange={(e) => handleInputChange('low_stock_threshold', parseInt(e.target.value) || 0)}
                className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                placeholder="5"
              />
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Weight (grams)
          </label>
          <input
            type="number"
            step="0.1"
            min="0"
            value={form.weight_grams}
            onChange={(e) => handleInputChange('weight_grams', parseFloat(e.target.value) || 0)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="0.0"
          />
        </div>
      </div>
    </div>
  )

  const renderJewelryDetails = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Jewelry Details</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Metal Type
          </label>
          <select
            value={form.metal_type}
            onChange={(e) => handleInputChange('metal_type', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select metal type</option>
            <option value="gold">Gold</option>
            <option value="silver">Silver</option>
            <option value="platinum">Platinum</option>
            <option value="rose-gold">Rose Gold</option>
            <option value="white-gold">White Gold</option>
            <option value="stainless-steel">Stainless Steel</option>
            <option value="titanium">Titanium</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Metal Purity
          </label>
          <select
            value={form.metal_purity}
            onChange={(e) => handleInputChange('metal_purity', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select purity</option>
            <option value="24k">24K</option>
            <option value="22k">22K</option>
            <option value="18k">18K</option>
            <option value="14k">14K</option>
            <option value="10k">10K</option>
            <option value="925">925 Sterling</option>
            <option value="950">950 Platinum</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Gemstone Type
          </label>
          <select
            value={form.gemstone_type}
            onChange={(e) => handleInputChange('gemstone_type', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select gemstone</option>
            <option value="diamond">Diamond</option>
            <option value="ruby">Ruby</option>
            <option value="sapphire">Sapphire</option>
            <option value="emerald">Emerald</option>
            <option value="pearl">Pearl</option>
            <option value="amethyst">Amethyst</option>
            <option value="topaz">Topaz</option>
            <option value="garnet">Garnet</option>
            <option value="opal">Opal</option>
            <option value="turquoise">Turquoise</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Gemstone Carat
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            value={form.gemstone_carat}
            onChange={(e) => handleInputChange('gemstone_carat', parseFloat(e.target.value) || 0)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="0.00"
          />
        </div>
      </div>

      <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-purple-900 dark:text-purple-200 mb-2">💎 Jewelry Specifications</h4>
        <p className="text-sm text-purple-800 dark:text-purple-300">
          These details help customers understand the quality and value of your jewelry pieces.
          Accurate specifications build trust and help with search filtering.
        </p>
      </div>
    </div>
  )

  const renderImages = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Product Images</h3>
      </div>

      {/* Upload Zone */}
      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-4">
          <label className="cursor-pointer">
            <span className="text-sm font-medium text-purple-600 hover:text-purple-500">
              {uploadingImages ? 'Uploading...' : 'Upload images'}
            </span>
            <input
              type="file"
              multiple
              accept="image/*"
              className="hidden"
              onChange={(e) => handleImageUpload(e.target.files)}
              disabled={uploadingImages}
            />
          </label>
          <span className="text-sm text-gray-500"> or drag and drop</span>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          PNG, JPG, GIF up to 10MB each
        </p>
      </div>

      {/* Image Grid */}
      {form.images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {form.images.map((image, index) => (
            <div key={index} className="relative group">
              <img
                src={image}
                alt={`Product image ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
              />

              {/* Main Image Badge */}
              {index === 0 && (
                <div className="absolute top-2 left-2 bg-purple-600 text-white text-xs px-2 py-1 rounded">
                  Main
                </div>
              )}

              {/* Remove Button */}
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">📸 Image Tips</h4>
        <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
          <li>• Use high-resolution images (at least 1000x1000 pixels)</li>
          <li>• Show multiple angles and close-up details</li>
          <li>• The first image will be the main product thumbnail</li>
          <li>• Include lifestyle shots showing the jewelry being worn</li>
        </ul>
      </div>
    </div>
  )

  const renderSEO = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">SEO & Metadata</h3>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            SEO Title
          </label>
          <input
            type="text"
            value={form.seo_title}
            onChange={(e) => handleInputChange('seo_title', e.target.value)}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="SEO title for search engines"
          />
          <p className="mt-1 text-sm text-gray-500">
            {form.seo_title.length}/60 characters
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            SEO Description
          </label>
          <textarea
            value={form.seo_description}
            onChange={(e) => handleInputChange('seo_description', e.target.value)}
            rows={3}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500"
            placeholder="SEO description for search engines"
          />
          <p className="mt-1 text-sm text-gray-500">
            {form.seo_description.length}/160 characters
          </p>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_featured"
            checked={form.is_featured}
            onChange={(e) => handleInputChange('is_featured', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-900 dark:text-white">
            Feature this product on homepage
          </label>
        </div>
      </div>

      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-green-900 dark:text-green-200 mb-2">🔍 SEO Tips</h4>
        <ul className="text-sm text-green-800 dark:text-green-300 space-y-1">
          <li>• Keep titles under 60 characters for best display</li>
          <li>• Keep descriptions under 160 characters</li>
          <li>• Include relevant keywords naturally</li>
          <li>• Make titles and descriptions compelling for clicks</li>
        </ul>
      </div>
    </div>
  )

  const renderPreview = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Product Preview</h3>
      </div>

      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          {/* Product Image */}
          <div className="aspect-square bg-gray-200 dark:bg-gray-700">
            {form.images.length > 0 ? (
              <img
                src={form.images[0]}
                alt={form.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <PhotoIcon className="h-16 w-16 text-gray-400" />
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="p-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {form.name || 'Product Name'}
            </h4>

            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {form.short_description || 'Short description will appear here'}
            </p>

            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center space-x-2">
                <span className="text-xl font-bold text-gray-900 dark:text-white">
                  ${form.price || '0.00'}
                </span>
                {form.compare_at_price > 0 && form.compare_at_price > form.price && (
                  <span className="text-sm text-gray-500 line-through">
                    ${form.compare_at_price}
                  </span>
                )}
              </div>

              {form.is_featured && (
                <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                  Featured
                </span>
              )}
            </div>

            {form.metal_type && (
              <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Material:</span> {form.metal_type}
                {form.metal_purity && ` (${form.metal_purity})`}
              </div>
            )}

            {form.gemstone_type && (
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Gemstone:</span> {form.gemstone_type}
                {form.gemstone_carat > 0 && ` (${form.gemstone_carat} ct)`}
              </div>
            )}

            <div className="mt-3 text-xs text-gray-500">
              SKU: {form.sku || 'Auto-generated'}
            </div>
          </div>
        </div>
      </div>

      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          This is how your product will appear to customers
        </p>
      </div>
    </div>
  )

  // Render content based on active tab
  switch (activeTab) {
    case 'basic':
      return renderBasicInfo()
    case 'pricing':
      return renderPricing()
    case 'inventory':
      return renderInventory()
    case 'jewelry':
      return renderJewelryDetails()
    case 'images':
      return renderImages()
    case 'seo':
      return renderSEO()
    case 'preview':
      return renderPreview()
    default:
      return renderBasicInfo()
  }
}
