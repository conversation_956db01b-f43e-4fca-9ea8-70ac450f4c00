{"name": "womanza-admin-panel", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.17.0", "@tanstack/react-router": "^1.15.0", "@tanstack/router-devtools": "^1.125.6", "@types/uuid": "^10.0.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@tanstack/router-vite-plugin": "^1.125.6", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0"}}