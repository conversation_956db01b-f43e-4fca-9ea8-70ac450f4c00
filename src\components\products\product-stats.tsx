import { XMarkIcon } from '@heroicons/react/24/outline'
import { 
  ShoppingBagIcon,
  CheckCircleIcon,
  DocumentIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  StarIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/solid'
import type { ProductStats } from '@/types/product'

interface ProductStatsProps {
  stats: ProductStats
  isLoading: boolean
  onClose: () => void
}

export function ProductStats({ stats, isLoading, onClose }: ProductStatsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const statCards = [
    {
      title: 'Total Products',
      value: stats.total_products.toLocaleString(),
      icon: ShoppingBagIcon,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Products',
      value: stats.active_products.toLocaleString(),
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Draft Products',
      value: stats.draft_products.toLocaleString(),
      icon: DocumentIcon,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Low Stock Items',
      value: stats.low_stock_products.toLocaleString(),
      icon: ExclamationTriangleIcon,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Out of Stock',
      value: stats.out_of_stock_products.toLocaleString(),
      icon: XCircleIcon,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Featured Products',
      value: stats.featured_products.toLocaleString(),
      icon: StarIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Total Inventory Value',
      value: formatCurrency(stats.total_value),
      icon: CurrencyDollarIcon,
      color: 'bg-emerald-500',
      textColor: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
    },
    {
      title: 'Average Price',
      value: formatCurrency(stats.average_price),
      icon: ChartBarIcon,
      color: 'bg-indigo-500',
      textColor: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ]

  if (isLoading) {
    return (
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-admin-text-primary">Product Statistics</h3>
          <button
            onClick={onClose}
            className="text-admin-text-secondary hover:text-admin-text-primary"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="bg-admin-bg-primary border border-admin-border rounded-lg p-4">
              <div className="animate-pulse">
                <div className="flex items-center justify-between mb-2">
                  <div className="h-4 bg-admin-border rounded w-20"></div>
                  <div className="h-8 w-8 bg-admin-border rounded"></div>
                </div>
                <div className="h-6 bg-admin-border rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-admin-text-primary">Product Statistics</h3>
        <button
          onClick={onClose}
          className="text-admin-text-secondary hover:text-admin-text-primary transition-colors"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <div
            key={index}
            className="bg-admin-bg-primary border border-admin-border rounded-lg p-4 hover:shadow-sm transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-admin-text-secondary mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-semibold text-admin-text-primary">
                  {stat.value}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.textColor}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Insights */}
      <div className="mt-6 pt-6 border-t border-admin-border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <p className="text-admin-text-secondary">Stock Health</p>
            <p className="text-lg font-semibold text-admin-text-primary mt-1">
              {stats.total_products > 0 
                ? Math.round(((stats.total_products - stats.out_of_stock_products) / stats.total_products) * 100)
                : 0
              }%
            </p>
            <p className="text-xs text-admin-text-secondary">Products in stock</p>
          </div>
          
          <div className="text-center">
            <p className="text-admin-text-secondary">Active Rate</p>
            <p className="text-lg font-semibold text-admin-text-primary mt-1">
              {stats.total_products > 0 
                ? Math.round((stats.active_products / stats.total_products) * 100)
                : 0
              }%
            </p>
            <p className="text-xs text-admin-text-secondary">Products published</p>
          </div>
          
          <div className="text-center">
            <p className="text-admin-text-secondary">Featured Rate</p>
            <p className="text-lg font-semibold text-admin-text-primary mt-1">
              {stats.total_products > 0 
                ? Math.round((stats.featured_products / stats.total_products) * 100)
                : 0
              }%
            </p>
            <p className="text-xs text-admin-text-secondary">Products featured</p>
          </div>
        </div>
      </div>
    </div>
  )
}
