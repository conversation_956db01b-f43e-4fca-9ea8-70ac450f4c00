-- Simple Users Table Setup for Womanza Admin Panel
-- Run this in your Supabase SQL Editor

-- STEP 1: Clean up existing foreign key constraints
-- Remove any foreign key constraints that might cause issues
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS fk_store_users_user_id;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey1;

-- Drop existing users table if it exists (for clean setup)
DROP TABLE IF EXISTS users CASCADE;

-- Create users table for storing user details (no foreign key to auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY,  -- We'll manually set this
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table (store users can view users in their store)
DROP POLICY IF EXISTS "Store users can view users in their store" ON users;
CREATE POLICY "Store users can view users in their store" ON users
    FOR SELECT USING (
        id IN (
            SELECT su.user_id
            FROM store_users su
            WHERE su.store_id IN (
                SELECT store_id
                FROM store_users
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create policy for inserting users (only super_admin and admin)
DROP POLICY IF EXISTS "Store admins can create users" ON users;
CREATE POLICY "Store admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1
            FROM store_users su
            WHERE su.user_id = auth.uid()
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Create updated_at trigger for users
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Update store_users table to ensure it has proper structure
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Make sure store_users.user_id is just UUID without foreign key constraint
-- This allows us to create users independently of auth.users
ALTER TABLE store_users ALTER COLUMN user_id TYPE UUID;

-- Create trigger for store_users updated_at
DROP TRIGGER IF EXISTS update_store_users_updated_at ON store_users;
CREATE TRIGGER update_store_users_updated_at
    BEFORE UPDATE ON store_users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
