import React, { useState, useRef, useEffect } from 'react'
import { 
  MagnifyingGlassIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  Cog6ToothIcon,
  ClockIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'

interface SearchResult {
  id: string
  title: string
  subtitle: string
  category: 'products' | 'orders' | 'customers' | 'settings'
  url: string
}

interface GlobalSearchProps {
  onClose: () => void
}

const categoryIcons = {
  products: ShoppingBagIcon,
  orders: ClipboardDocumentListIcon,
  customers: UsersIcon,
  settings: Cog6ToothIcon,
}

const categoryLabels = {
  products: 'Products',
  orders: 'Orders',
  customers: 'Customers',
  settings: 'Settings',
}

// TODO: Implement real search functionality with Supabase
const recentSearches: string[] = []

export function GlobalSearch({ onClose }: GlobalSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const { currentStore } = useStore()

  useEffect(() => {
    // Focus the input when the component mounts
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  useEffect(() => {
    if (query.trim()) {
      setIsLoading(true)
      // TODO: Implement real search with Supabase
      const timer = setTimeout(() => {
        setResults([])
        setIsLoading(false)
      }, 300)

      return () => clearTimeout(timer)
    } else {
      setResults([])
      setIsLoading(false)
    }
  }, [query])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleResultClick = (result: SearchResult) => {
    // TODO: Implement navigation to result URL
    onClose()
  }

  const groupedResults = results.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = []
    }
    acc[result.category].push(result)
    return acc
  }, {} as Record<string, SearchResult[]>)

  return (
    <div className="absolute top-full left-0 mt-1 w-96 bg-admin-bg-secondary border border-admin-border rounded-lg shadow-lg z-50">
      {/* Search Input */}
      <div className="p-4 border-b border-admin-border">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-admin-text-muted" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Search products, orders, customers..."
            className="w-full pl-10 pr-4 py-2 bg-admin-bg-primary border border-admin-border rounded-md text-sm text-admin-text-primary placeholder-admin-text-muted focus:outline-none focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* Search Results */}
      <div className="max-h-80 overflow-y-auto">
        {query.trim() ? (
          <div className="py-2">
            {isLoading ? (
              <div className="px-4 py-8 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-admin-primary mx-auto mb-2"></div>
                <p className="text-sm text-admin-text-muted">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              Object.entries(groupedResults).map(([category, categoryResults]) => {
                const Icon = categoryIcons[category as keyof typeof categoryIcons]
                return (
                  <div key={category} className="mb-4">
                    <div className="px-4 py-2 text-xs font-semibold text-admin-text-muted uppercase tracking-wide">
                      {categoryLabels[category as keyof typeof categoryLabels]}
                    </div>
                    {categoryResults.map((result) => (
                      <button
                        key={result.id}
                        onClick={() => handleResultClick(result)}
                        className="w-full flex items-center gap-3 px-4 py-2 hover:bg-admin-hover transition-colors"
                      >
                        <Icon className="h-4 w-4 text-admin-primary flex-shrink-0" />
                        <div className="text-left flex-1">
                          <p className="text-sm font-medium text-admin-text-primary">
                            {result.title}
                          </p>
                          <p className="text-xs text-admin-text-muted">
                            {result.subtitle}
                          </p>
                        </div>
                      </button>
                    ))}
                  </div>
                )
              })
            ) : (
              <div className="px-4 py-8 text-center">
                <MagnifyingGlassIcon className="h-8 w-8 text-admin-text-muted mx-auto mb-2" />
                <p className="text-sm text-admin-text-muted">No results found</p>
                <p className="text-xs text-admin-text-muted mt-1">
                  Try searching for products, orders, or customers
                </p>
              </div>
            )}
          </div>
        ) : (
          /* Recent Searches */
          <div className="py-2">
            <div className="px-4 py-2 text-xs font-semibold text-admin-text-muted uppercase tracking-wide">
              Recent Searches
            </div>
            {recentSearches.map((search, index) => (
              <button
                key={index}
                onClick={() => setQuery(search)}
                className="w-full flex items-center gap-3 px-4 py-2 hover:bg-admin-hover transition-colors"
              >
                <ClockIcon className="h-4 w-4 text-admin-text-muted flex-shrink-0" />
                <span className="text-sm text-admin-text-primary">{search}</span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-3 border-t border-admin-border">
        <div className="flex items-center justify-between text-xs text-admin-text-muted">
          <span>Press ESC to close</span>
          <span>Search in {currentStore?.name}</span>
        </div>
      </div>
    </div>
  )
}
