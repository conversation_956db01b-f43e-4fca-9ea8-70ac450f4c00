import { createRootRoute, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/router-devtools'
import { Toaster } from 'react-hot-toast'
import { env } from '@/lib/env'

export const Route = createRootRoute({
  component: () => (
    <>
      <Outlet />
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          className: 'dark:bg-gray-800 dark:text-white',
          style: {
            background: 'var(--admin-bg-secondary)',
            color: 'var(--admin-text-primary)',
            border: '1px solid var(--admin-border)',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: 'var(--admin-success)',
              secondary: 'var(--admin-bg-primary)',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: 'var(--admin-error)',
              secondary: 'var(--admin-bg-primary)',
            },
          },
        }}
      />
      {env.VITE_DEBUG_MODE && <TanStackRouterDevtools />}
    </>
  ),
})
