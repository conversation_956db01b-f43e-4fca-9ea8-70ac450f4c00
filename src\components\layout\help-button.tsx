import React, { useState, useEffect } from 'react'
import {
  QuestionMarkCircleIcon,
  BookOpenIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  LifebuoyIcon,
  ArrowTopRightOnSquareIcon,
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'

interface HelpItem {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
  external?: boolean
}

export function HelpButton() {
  const [showDropdown, setShowDropdown] = useState(false)

  const helpItems: HelpItem[] = [
    {
      id: 'documentation',
      title: 'Documentation',
      description: 'Browse the complete admin guide',
      icon: BookOpenIcon,
      action: () => {
        window.open('https://docs.womanza.com', '_blank')
        setShowDropdown(false)
      },
      external: true,
    },
    {
      id: 'video-tutorials',
      title: 'Video Tutorials',
      description: 'Watch step-by-step guides',
      icon: VideoCameraIcon,
      action: () => {
        window.open('https://tutorials.womanza.com', '_blank')
        setShowDropdown(false)
      },
      external: true,
    },
    {
      id: 'live-chat',
      title: 'Live Chat Support',
      description: 'Chat with our support team',
      icon: ChatBubbleLeftRightIcon,
      action: () => {
        // In a real app, this would open a chat widget
        console.log('Opening live chat...')
        setShowDropdown(false)
      },
    },
    {
      id: 'knowledge-base',
      title: 'Knowledge Base',
      description: 'Search frequently asked questions',
      icon: DocumentTextIcon,
      action: () => {
        window.open('https://help.womanza.com', '_blank')
        setShowDropdown(false)
      },
      external: true,
    },
    {
      id: 'contact-support',
      title: 'Contact Support',
      description: 'Submit a support ticket',
      icon: LifebuoyIcon,
      action: () => {
        // In a real app, this would open a support form
        console.log('Opening support form...')
        setShowDropdown(false)
      },
    },
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDropdown) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showDropdown])

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowDropdown(!showDropdown)}
        className="p-2 hover:bg-admin-hover"
        title="Help & Support"
      >
        <QuestionMarkCircleIcon className="h-5 w-5" />
      </Button>

      {showDropdown && (
        <div className="absolute top-full right-0 mt-1 w-72 bg-admin-bg-secondary border border-admin-border rounded-lg shadow-lg z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-admin-border">
            <h3 className="text-sm font-semibold text-admin-text-primary">
              Help & Support
            </h3>
            <p className="text-xs text-admin-text-muted mt-1">
              Get help with using the admin panel
            </p>
          </div>

          {/* Help Items */}
          <div className="py-2">
            {helpItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={item.action}
                  className="w-full flex items-center gap-3 px-4 py-3 hover:bg-admin-hover transition-colors"
                >
                  <Icon className="h-5 w-5 text-admin-primary flex-shrink-0" />
                  <div className="text-left flex-1">
                    <div className="flex items-center gap-1">
                      <p className="text-sm font-medium text-admin-text-primary">
                        {item.title}
                      </p>
                      {item.external && (
                        <ArrowTopRightOnSquareIcon className="h-3 w-3 text-admin-text-muted" />
                      )}
                    </div>
                    <p className="text-xs text-admin-text-muted">
                      {item.description}
                    </p>
                  </div>
                </button>
              )
            })}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 border-t border-admin-border">
            <div className="text-xs text-admin-text-muted">
              <p>Need immediate help?</p>
              <p className="mt-1">
                Email us at{' '}
                <a 
                  href="mailto:<EMAIL>"
                  className="text-admin-primary hover:text-admin-primary/80"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
