-- User Management System for Womanza Admin Panel
-- This migration creates tables for user invitations and activity logging

-- First, let's check if the user_invites table exists and add missing columns
DO $$
BEGIN
    -- Create user_invites table if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_invites') THEN
        CREATE TABLE user_invites (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
            email TEXT NOT NULL,
            role user_role NOT NULL,
            token TEXT NOT NULL UNIQUE,
            expires_at TIMESTAMPTZ NOT NULL,
            invited_by UUID REFERENCES auth.users(id),
            accepted_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
    END IF;

    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'user_invites' AND column_name = 'status') THEN
        ALTER TABLE user_invites ADD COLUMN status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'revoked', 'expired'));
    END IF;
END $$;

-- Create user_activity_logs table
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_invites_store_id ON user_invites(store_id);
CREATE INDEX IF NOT EXISTS idx_user_invites_email ON user_invites(email);
CREATE INDEX IF NOT EXISTS idx_user_invites_token ON user_invites(token);
CREATE INDEX IF NOT EXISTS idx_user_invites_status ON user_invites(status);
CREATE INDEX IF NOT EXISTS idx_user_invites_expires_at ON user_invites(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_activity_logs_store_id ON user_activity_logs(store_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action ON user_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at);

-- Create updated_at trigger for user_invites
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_invites_updated_at 
    BEFORE UPDATE ON user_invites 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies for user_invites
ALTER TABLE user_invites ENABLE ROW LEVEL SECURITY;

-- Super admins and admins can manage invites for their store
CREATE POLICY "Store admins can manage invites" ON user_invites
    FOR ALL USING (
        store_id IN (
            SELECT su.store_id 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Users can view their own pending invites (for accepting invitations)
CREATE POLICY "Users can view their own invites" ON user_invites
    FOR SELECT USING (
        email = (SELECT email FROM auth.users WHERE id = auth.uid())
        AND status = 'pending'
        AND expires_at > NOW()
    );

-- RLS Policies for user_activity_logs
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

-- Store users can view activity logs for their store
CREATE POLICY "Store users can view activity logs" ON user_activity_logs
    FOR SELECT USING (
        store_id IN (
            SELECT su.store_id 
            FROM store_users su 
            WHERE su.user_id = auth.uid()
        )
    );

-- All authenticated users can insert their own activity logs
CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
    FOR INSERT WITH CHECK (
        user_id = auth.uid()
        AND store_id IN (
            SELECT su.store_id 
            FROM store_users su 
            WHERE su.user_id = auth.uid()
        )
    );

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
    UPDATE user_invites 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' 
    AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old activity logs (keep last 1000 per store)
CREATE OR REPLACE FUNCTION cleanup_old_activity_logs()
RETURNS void AS $$
BEGIN
    DELETE FROM user_activity_logs 
    WHERE id NOT IN (
        SELECT id FROM (
            SELECT id, ROW_NUMBER() OVER (
                PARTITION BY store_id 
                ORDER BY created_at DESC
            ) as rn
            FROM user_activity_logs
        ) ranked
        WHERE rn <= 1000
    );
END;
$$ LANGUAGE plpgsql;

-- Create a function to handle invitation acceptance
CREATE OR REPLACE FUNCTION accept_invitation(invitation_token TEXT)
RETURNS JSONB AS $$
DECLARE
    invite_record user_invites%ROWTYPE;
    new_user_id UUID;
    result JSONB;
BEGIN
    -- Get the invitation
    SELECT * INTO invite_record
    FROM user_invites
    WHERE token = invitation_token
    AND status = 'pending'
    AND expires_at > NOW();

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid or expired invitation'
        );
    END IF;

    -- Get the current user ID
    new_user_id := auth.uid();
    
    IF new_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User must be authenticated'
        );
    END IF;

    -- Check if user email matches invitation
    IF (SELECT email FROM auth.users WHERE id = new_user_id) != invite_record.email THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Email does not match invitation'
        );
    END IF;

    -- Check if user already has access to this store
    IF EXISTS (
        SELECT 1 FROM store_users 
        WHERE user_id = new_user_id 
        AND store_id = invite_record.store_id
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User already has access to this store'
        );
    END IF;

    -- Create store_users record
    INSERT INTO store_users (store_id, user_id, role)
    VALUES (invite_record.store_id, new_user_id, invite_record.role);

    -- Mark invitation as accepted
    UPDATE user_invites
    SET status = 'accepted',
        accepted_at = NOW(),
        updated_at = NOW()
    WHERE id = invite_record.id;

    -- Log the activity
    INSERT INTO user_activity_logs (store_id, user_id, action, details)
    VALUES (
        invite_record.store_id,
        new_user_id,
        'invitation_accepted',
        jsonb_build_object(
            'role', invite_record.role,
            'invited_by', invite_record.invited_by
        )
    );

    RETURN jsonb_build_object(
        'success', true,
        'role', invite_record.role,
        'store_id', invite_record.store_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION accept_invitation(TEXT) TO authenticated;

-- Create a scheduled job to clean up expired invitations (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-expired-invitations', '0 2 * * *', 'SELECT expire_old_invitations();');
-- SELECT cron.schedule('cleanup-old-activity-logs', '0 3 * * 0', 'SELECT cleanup_old_activity_logs();');

-- Insert some sample data for testing (remove in production)
-- INSERT INTO user_invites (store_id, email, role, token, expires_at, invited_by)
-- VALUES (
--     (SELECT id FROM stores WHERE name = 'Womanza Jewelry Store' LIMIT 1),
--     '<EMAIL>',
--     'editor',
--     'test-token-' || gen_random_uuid(),
--     NOW() + INTERVAL '7 days',
--     (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
-- );
