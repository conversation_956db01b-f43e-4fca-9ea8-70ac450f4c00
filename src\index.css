@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme admin colors */
  --admin-primary: #f97316;
  --admin-secondary: #64748b;
  --admin-accent: #3b82f6;
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-error: #ef4444;
  --admin-info: #06b6d4;
  
  --admin-bg-primary: #ffffff;
  --admin-bg-secondary: #f8fafc;
  --admin-bg-tertiary: #f1f5f9;
  
  --admin-text-primary: #0f172a;
  --admin-text-secondary: #475569;
  --admin-text-muted: #94a3b8;
  
  --admin-border: #e2e8f0;
  --admin-hover: #f1f5f9;
}

.dark {
  /* Dark theme admin colors */
  --admin-primary: #fb923c;
  --admin-secondary: #94a3b8;
  --admin-accent: #60a5fa;
  --admin-success: #34d399;
  --admin-warning: #fbbf24;
  --admin-error: #f87171;
  --admin-info: #22d3ee;
  
  --admin-bg-primary: #0f172a;
  --admin-bg-secondary: #1e293b;
  --admin-bg-tertiary: #334155;
  
  --admin-text-primary: #f8fafc;
  --admin-text-secondary: #cbd5e1;
  --admin-text-muted: #64748b;
  
  --admin-border: #334155;
  --admin-hover: #1e293b;
}

@layer base {
  * {
    @apply border-admin-border;
  }
  
  body {
    @apply bg-admin-bg-primary text-admin-text-primary;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}

@layer components {
  .admin-card {
    @apply bg-admin-bg-secondary border border-admin-border rounded-lg shadow-sm;
  }
  
  .admin-button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-admin-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .admin-button-primary {
    @apply admin-button bg-admin-primary text-white hover:opacity-90;
  }

  .admin-button-secondary {
    @apply admin-button bg-admin-bg-secondary text-admin-text-primary border border-admin-border hover:bg-admin-hover;
  }
  
  .admin-input {
    @apply flex h-10 w-full rounded-md border border-admin-border bg-admin-bg-primary px-3 py-2 text-sm ring-offset-admin-bg-primary file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-admin-text-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-admin-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .admin-sidebar-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-admin-text-secondary transition-all hover:text-admin-text-primary hover:bg-admin-hover;
  }
  
  .admin-sidebar-item.active {
    @apply bg-admin-primary text-white hover:opacity-90 hover:text-white;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--admin-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--admin-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--admin-text-muted);
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-admin-primary focus:ring-offset-2 focus:ring-offset-admin-bg-primary;
}
