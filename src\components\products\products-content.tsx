import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useStore } from '@/contexts/store-context'
import { ProductService } from '@/services/product-service'
import { ProductsHeader } from './products-header'
import { ProductsFilters } from './products-filters'
import { ProductsTable } from './products-table'
import { ProductsGrid } from './products-grid'
import { ProductStats } from './product-stats'
import type { ProductFilter } from '@/types/product'
import {
  ViewColumnsIcon,
  Squares2X2Icon,
  FunnelIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

export function ProductsContent() {
  const { currentStore } = useStore()
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')
  const [showFilters, setShowFilters] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [filters, setFilters] = useState<ProductFilter>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  // Fetch products
  const { 
    data: productsData, 
    isLoading: productsLoading, 
    error: productsError,
    refetch: refetchProducts 
  } = useQuery({
    queryKey: ['products', currentStore?.id, filters],
    queryFn: () => ProductService.getProducts(currentStore!.id, filters),
    enabled: !!currentStore?.id,
  })

  // Fetch product stats
  const { 
    data: stats, 
    isLoading: statsLoading 
  } = useQuery({
    queryKey: ['product-stats', currentStore?.id],
    queryFn: () => ProductService.getProductStats(currentStore!.id),
    enabled: !!currentStore?.id && showStats,
  })

  // Fetch categories for filters
  const { data: categories } = useQuery({
    queryKey: ['categories', currentStore?.id],
    queryFn: () => ProductService.getCategories(currentStore!.id),
    enabled: !!currentStore?.id,
  })

  const handleFilterChange = (newFilters: Partial<ProductFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }))
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handleRefresh = () => {
    refetchProducts()
  }

  if (!currentStore) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-admin-text-secondary">Please select a store to manage products</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <ProductsHeader
        onRefresh={handleRefresh}
        totalProducts={productsData?.count || 0}
      />

      {/* Stats Panel */}
      {showStats && stats && (
        <ProductStats 
          stats={stats} 
          isLoading={statsLoading}
          onClose={() => setShowStats(false)}
        />
      )}

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-admin-bg-secondary rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'table'
                  ? 'bg-admin-primary text-white'
                  : 'text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover'
              }`}
              title="Table view"
            >
              <ViewColumnsIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-admin-primary text-white'
                  : 'text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover'
              }`}
              title="Grid view"
            >
              <Squares2X2Icon className="h-4 w-4" />
            </button>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-3 py-2 text-sm rounded-md border transition-colors ${
              showFilters
                ? 'bg-admin-primary text-white border-admin-primary'
                : 'text-admin-text-secondary border-admin-border hover:text-admin-text-primary hover:bg-admin-hover'
            }`}
          >
            <FunnelIcon className="h-4 w-4" />
            Filters
          </button>

          {/* Stats Toggle */}
          <button
            onClick={() => setShowStats(!showStats)}
            className={`flex items-center gap-2 px-3 py-2 text-sm rounded-md border transition-colors ${
              showStats
                ? 'bg-admin-primary text-white border-admin-primary'
                : 'text-admin-text-secondary border-admin-border hover:text-admin-text-primary hover:bg-admin-hover'
            }`}
          >
            <ChartBarIcon className="h-4 w-4" />
            Stats
          </button>
        </div>

        {/* Results Info */}
        <div className="text-sm text-admin-text-secondary">
          {productsLoading ? (
            'Loading...'
          ) : productsError ? (
            'Error loading products'
          ) : (
            `${productsData?.count || 0} products found`
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <ProductsFilters
          filters={filters}
          categories={categories || []}
          onFilterChange={handleFilterChange}
          onReset={() => setFilters({
            page: 1,
            limit: 20,
            sort_by: 'created_at',
            sort_order: 'desc'
          })}
        />
      )}

      {/* Products List */}
      {viewMode === 'table' ? (
        <ProductsTable
          products={productsData?.data || []}
          isLoading={productsLoading}
          error={productsError}
          pagination={{
            page: filters.page || 1,
            limit: filters.limit || 20,
            total: productsData?.count || 0,
            totalPages: productsData?.totalPages || 1,
          }}
          onPageChange={handlePageChange}
          onRefresh={handleRefresh}
        />
      ) : (
        <ProductsGrid
          products={productsData?.data || []}
          isLoading={productsLoading}
          error={productsError}
          pagination={{
            page: filters.page || 1,
            limit: filters.limit || 20,
            total: productsData?.count || 0,
            totalPages: productsData?.totalPages || 1,
          }}
          onPageChange={handlePageChange}
          onRefresh={handleRefresh}
        />
      )}
    </div>
  )
}
