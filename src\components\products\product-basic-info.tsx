import { UseFormReturn } from 'react-hook-form'
import type { ProductFormData, Category } from '@/types/product'

interface ProductBasicInfoProps {
  form: UseFormReturn<ProductFormData>
  categories: Category[]
}

export function ProductBasicInfo({ form, categories }: ProductBasicInfoProps) {
  const { register, formState: { errors }, watch, setValue } = form

  const watchedName = watch('name')

  const handleSlugGenerate = () => {
    if (watchedName) {
      const slug = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      setValue('slug', slug)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          Basic Information
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Enter the basic details for your product including name, description, and category.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Product Name */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Product Name *
          </label>
          <input
            type="text"
            {...register('name')}
            className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            placeholder="Enter product name..."
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        {/* Product Slug */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Product Slug *
          </label>
          <div className="flex">
            <input
              type="text"
              {...register('slug')}
              className="flex-1 px-3 py-2 border border-admin-border rounded-l-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="product-slug"
            />
            <button
              type="button"
              onClick={handleSlugGenerate}
              className="px-3 py-2 bg-admin-border text-admin-text-secondary border border-l-0 border-admin-border rounded-r-md hover:bg-admin-hover transition-colors text-sm"
            >
              Generate
            </button>
          </div>
          {errors.slug && (
            <p className="mt-1 text-sm text-red-600">{errors.slug.message}</p>
          )}
          <p className="mt-1 text-xs text-admin-text-secondary">
            URL-friendly version of the product name
          </p>
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Category
          </label>
          <select
            {...register('category_id')}
            className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* SKU */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            SKU (Stock Keeping Unit)
          </label>
          <input
            type="text"
            {...register('sku')}
            className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            placeholder="Enter SKU..."
          />
          <p className="mt-1 text-xs text-admin-text-secondary">
            Unique identifier for inventory tracking
          </p>
        </div>

        {/* Barcode */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Barcode
          </label>
          <input
            type="text"
            {...register('barcode')}
            className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            placeholder="Enter barcode..."
          />
        </div>
      </div>

      {/* Short Description */}
      <div>
        <label className="block text-sm font-medium text-admin-text-primary mb-2">
          Short Description
        </label>
        <textarea
          {...register('short_description')}
          rows={3}
          className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          placeholder="Brief description for product listings..."
        />
        <p className="mt-1 text-xs text-admin-text-secondary">
          Brief description shown in product listings (recommended: 150-160 characters)
        </p>
      </div>

      {/* Full Description */}
      <div>
        <label className="block text-sm font-medium text-admin-text-primary mb-2">
          Full Description
        </label>
        <textarea
          {...register('description')}
          rows={8}
          className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          placeholder="Detailed product description..."
        />
        <p className="mt-1 text-xs text-admin-text-secondary">
          Detailed description shown on product pages. You can use markdown formatting.
        </p>
      </div>

      {/* Product Status Options */}
      <div>
        <label className="block text-sm font-medium text-admin-text-primary mb-3">
          Product Options
        </label>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register('is_featured')}
              className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
            />
            <span className="ml-2 text-sm text-admin-text-primary">Featured Product</span>
            <span className="ml-2 text-xs text-admin-text-secondary">
              Show this product prominently on your store
            </span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              {...register('requires_shipping')}
              className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
            />
            <span className="ml-2 text-sm text-admin-text-primary">Requires Shipping</span>
            <span className="ml-2 text-xs text-admin-text-secondary">
              This product needs to be shipped to customers
            </span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              {...register('is_digital')}
              className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
            />
            <span className="ml-2 text-sm text-admin-text-primary">Digital Product</span>
            <span className="ml-2 text-xs text-admin-text-secondary">
              This is a digital product (no physical shipping required)
            </span>
          </label>
        </div>
      </div>
    </div>
  )
}
