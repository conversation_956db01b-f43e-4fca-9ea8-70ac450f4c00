import { UseFormReturn } from 'react-hook-form'
import type { ProductFormData } from '@/types/product'

interface ProductInventoryProps {
  form: UseFormReturn<ProductFormData>
}

export function ProductInventory({ form }: ProductInventoryProps) {
  const { register, formState: { errors }, watch } = form

  const trackInventory = watch('track_inventory')
  const inventoryQuantity = watch('inventory_quantity')
  const lowStockThreshold = watch('low_stock_threshold')

  const getStockStatus = () => {
    if (!trackInventory) return { color: 'text-gray-500', text: 'Not tracked' }
    if (inventoryQuantity === 0) return { color: 'text-red-600', text: 'Out of stock' }
    if (inventoryQuantity <= lowStockThreshold) return { color: 'text-orange-600', text: 'Low stock' }
    return { color: 'text-green-600', text: 'In stock' }
  }

  const stockStatus = getStockStatus()

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          Inventory Management
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Manage stock levels, tracking settings, and inventory alerts for this product.
        </p>
      </div>

      {/* Track Inventory Toggle */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            {...register('track_inventory')}
            className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
          />
          <span className="ml-3 text-sm font-medium text-admin-text-primary">
            Track inventory for this product
          </span>
        </label>
        <p className="mt-2 text-xs text-admin-text-secondary ml-6">
          Enable this to track stock levels and receive low stock alerts
        </p>
      </div>

      {trackInventory && (
        <div className="space-y-6">
          {/* Current Stock Status */}
          <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-admin-text-primary">
                  Current Stock Status
                </h4>
                <p className={`text-sm font-semibold ${stockStatus.color}`}>
                  {stockStatus.text}
                </p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-admin-text-primary">
                  {inventoryQuantity || 0}
                </p>
                <p className="text-xs text-admin-text-secondary">units available</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Inventory Quantity */}
            <div>
              <label className="block text-sm font-medium text-admin-text-primary mb-2">
                Current Stock Quantity *
              </label>
              <input
                type="number"
                min="0"
                {...register('inventory_quantity', { valueAsNumber: true })}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0"
              />
              {errors.inventory_quantity && (
                <p className="mt-1 text-sm text-red-600">{errors.inventory_quantity.message}</p>
              )}
              <p className="mt-1 text-xs text-admin-text-secondary">
                Number of units currently in stock
              </p>
            </div>

            {/* Low Stock Threshold */}
            <div>
              <label className="block text-sm font-medium text-admin-text-primary mb-2">
                Low Stock Alert Threshold
              </label>
              <input
                type="number"
                min="0"
                {...register('low_stock_threshold', { valueAsNumber: true })}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="5"
              />
              <p className="mt-1 text-xs text-admin-text-secondary">
                Get notified when stock falls below this number
              </p>
            </div>
          </div>

          {/* Backorder Settings */}
          <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-4">
            <label className="flex items-start">
              <input
                type="checkbox"
                {...register('allow_backorder')}
                className="mt-1 rounded border-admin-border text-admin-primary focus:ring-admin-primary"
              />
              <div className="ml-3">
                <span className="text-sm font-medium text-admin-text-primary">
                  Allow backorders
                </span>
                <p className="text-xs text-admin-text-secondary mt-1">
                  Customers can purchase this product even when it's out of stock. 
                  They will be notified about the expected delivery time.
                </p>
              </div>
            </label>
          </div>

          {/* Inventory Alerts */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-admin-text-primary">
              Inventory Alerts
            </h4>
            
            {inventoryQuantity <= lowStockThreshold && inventoryQuantity > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-orange-800">
                      Low Stock Alert
                    </h3>
                    <p className="text-sm text-orange-700 mt-1">
                      This product is running low on stock. Consider restocking soon.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {inventoryQuantity === 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Out of Stock
                    </h3>
                    <p className="text-sm text-red-700 mt-1">
                      This product is currently out of stock and unavailable for purchase.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {!trackInventory && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
          <div className="text-gray-400 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">
            Inventory Tracking Disabled
          </h3>
          <p className="text-sm text-gray-500">
            Enable inventory tracking to manage stock levels and receive alerts.
          </p>
        </div>
      )}

      {/* Inventory Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          📦 Inventory Management Tips
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Set low stock thresholds based on your reorder lead times</li>
          <li>• Enable backorders for popular items to avoid lost sales</li>
          <li>• Regularly audit physical inventory against system counts</li>
          <li>• Consider seasonal demand when setting stock levels</li>
          <li>• Use inventory reports to identify fast and slow-moving items</li>
        </ul>
      </div>
    </div>
  )
}
