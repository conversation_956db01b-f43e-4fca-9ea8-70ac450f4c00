-- 🚀 Simple Users Setup - Clean Approach
-- Run this script in your Supabase SQL Editor

-- STEP 1: Drop everything to start clean
DROP POLICY IF EXISTS "Store users can view users in their store" ON users;
DROP POLICY IF EXISTS "Store admins can create users" ON users;
DROP TABLE IF EXISTS users CASCADE;

-- STEP 2: Remove foreign key constraints from store_users
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS fk_store_users_user_id;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey1;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey2;

-- STEP 3: Create simple users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- STEP 4: Enable RLS but with simple policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Simple policy: allow all operations for authenticated users
CREATE POLICY "Allow all for authenticated users" ON users
    FOR ALL USING (auth.role() = 'authenticated');

-- STEP 5: Create basic indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- STEP 6: Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Success message
SELECT 'Users table created successfully!' as status;
