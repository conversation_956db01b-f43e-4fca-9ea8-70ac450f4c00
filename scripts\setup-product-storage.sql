-- 🖼️ Setup Product Image Storage
-- Run this script in your Supabase SQL Editor

-- STEP 1: Create storage bucket for product images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'product-images',
  'product-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- STEP 2: Create RLS policies for the bucket
-- Allow authenticated users to upload images
CREATE POLICY "Allow authenticated users to upload product images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Allow public read access to product images
CREATE POLICY "Allow public read access to product images" ON storage.objects
  FOR SELECT USING (bucket_id = 'product-images');

-- Allow authenticated users to delete their uploaded images
CREATE POLICY "Allow authenticated users to delete product images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- STEP 3: Ensure products table exists with proper structure
CREATE TABLE IF NOT EXISTS products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  store_id UUID NOT NULL,
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  short_description TEXT,
  sku TEXT,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  compare_at_price DECIMAL(10,2),
  cost_price DECIMAL(10,2),
  track_inventory BOOLEAN DEFAULT true,
  inventory_quantity INTEGER DEFAULT 0,
  low_stock_threshold INTEGER DEFAULT 5,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived')),
  is_featured BOOLEAN DEFAULT false,
  weight_grams DECIMAL(8,2),
  category_id UUID,
  images TEXT[] DEFAULT '{}',
  meta_title TEXT,
  meta_description TEXT,
  -- Jewelry specific fields
  metal_type TEXT,
  gemstone_type TEXT,
  gemstone_carat DECIMAL(8,2),
  metal_purity TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- STEP 4: Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- STEP 5: Create policies for products table
-- Allow authenticated users to view products
CREATE POLICY "Allow authenticated users to view products" ON products
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert products
CREATE POLICY "Allow authenticated users to insert products" ON products
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update products
CREATE POLICY "Allow authenticated users to update products" ON products
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Allow authenticated users to delete products
CREATE POLICY "Allow authenticated users to delete products" ON products
  FOR DELETE USING (auth.role() = 'authenticated');

-- STEP 6: Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  store_id UUID NOT NULL,
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  parent_id UUID REFERENCES categories(id),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on categories table
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create policies for categories table
CREATE POLICY "Allow authenticated users to manage categories" ON categories
  FOR ALL USING (auth.role() = 'authenticated');

-- STEP 7: Insert some default categories for jewelry store
INSERT INTO categories (store_id, name, slug, description, sort_order)
VALUES 
  ('550e8400-e29b-41d4-a716-************', 'Rings', 'rings', 'Beautiful rings for every occasion', 1),
  ('550e8400-e29b-41d4-a716-************', 'Necklaces', 'necklaces', 'Elegant necklaces and pendants', 2),
  ('550e8400-e29b-41d4-a716-************', 'Earrings', 'earrings', 'Stunning earrings collection', 3),
  ('550e8400-e29b-41d4-a716-************', 'Bracelets', 'bracelets', 'Stylish bracelets and bangles', 4),
  ('550e8400-e29b-41d4-a716-************', 'Watches', 'watches', 'Luxury and fashion watches', 5)
ON CONFLICT DO NOTHING;

-- STEP 8: Create updated_at trigger for products
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Drop trigger if exists and recreate for categories
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Success message
SELECT '✅ Product storage and database setup completed successfully!' as status;
SELECT 'You can now upload product images and create products!' as message;
