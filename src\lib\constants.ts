// Constants for Womanza Admin Panel (Single Store Mode)

// Single Store Configuration
export const WOMANZA_STORE = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  name: 'Womanza Jewelry Store',
  domain: 'womanza.com',
  description: 'Premium jewelry and accessories for the modern woman',
  contact_email: '<EMAIL>',
  contact_phone: '+****************',
  currency: 'USD',
  settings: {
    logo: '/logo.png',
    primary_color: '#D946EF', // Purple
    secondary_color: '#EC4899', // Pink
    accent_color: '#F59E0B', // Amber
    font_family: 'Inter',
    timezone: 'America/New_York',
    language: 'en',
    country: 'US',
  }
} as const

// Navigation permissions mapping
export const NAVIGATION_PERMISSIONS = {
  '/dashboard': 'view_analytics',
  '/products': 'view_products',
  '/products/categories': 'manage_categories',
  '/products/inventory': 'manage_inventory',
  '/products/new': 'manage_products',
  '/orders': 'view_orders',
  '/customers': 'view_customers',
  '/customers/groups': 'manage_customer_groups',
  '/marketing': 'manage_marketing',
  '/marketing/promotions': 'manage_promotions',
  '/marketing/emails': 'manage_email_campaigns',
  '/users': 'manage_users',
  '/settings': 'manage_store_settings',
  '/settings/store': 'manage_store_settings',
  '/settings/branding': 'manage_store_branding',
  '/settings/payments': 'manage_payment_settings',
  '/settings/shipping': 'manage_shipping_settings',
  '/settings/seo': 'manage_seo_settings',
} as const

// Quick actions permissions
export const QUICK_ACTION_PERMISSIONS = {
  'add_product': 'manage_products',
  'new_order': 'manage_orders',
  'add_customer': 'manage_customers',
  'create_promotion': 'manage_promotions',
  'invite_user': 'invite_users',
} as const

// Status badges and colors
export const STATUS_COLORS = {
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  primary: '#D946EF',
} as const

// Order statuses
export const ORDER_STATUSES = {
  pending: { label: 'Pending', color: 'warning' },
  processing: { label: 'Processing', color: 'info' },
  shipped: { label: 'Shipped', color: 'primary' },
  delivered: { label: 'Delivered', color: 'success' },
  cancelled: { label: 'Cancelled', color: 'error' },
  refunded: { label: 'Refunded', color: 'error' },
} as const

// Product statuses
export const PRODUCT_STATUSES = {
  active: { label: 'Active', color: 'success' },
  draft: { label: 'Draft', color: 'warning' },
  archived: { label: 'Archived', color: 'error' },
  out_of_stock: { label: 'Out of Stock', color: 'error' },
} as const

// Customer statuses
export const CUSTOMER_STATUSES = {
  active: { label: 'Active', color: 'success' },
  inactive: { label: 'Inactive', color: 'warning' },
  blocked: { label: 'Blocked', color: 'error' },
} as const

// Notification types
export const NOTIFICATION_TYPES = {
  order: { label: 'New Order', icon: 'ClipboardDocumentListIcon', color: 'info' },
  low_stock: { label: 'Low Stock', icon: 'ExclamationTriangleIcon', color: 'warning' },
  customer: { label: 'New Customer', icon: 'UsersIcon', color: 'success' },
  system: { label: 'System', icon: 'CogIcon', color: 'primary' },
} as const

// Default pagination
export const PAGINATION_DEFAULTS = {
  page: 1,
  limit: 20,
  maxLimit: 100,
} as const

// File upload limits
export const UPLOAD_LIMITS = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
  allowedDocumentTypes: ['application/pdf', 'text/csv', 'application/vnd.ms-excel'],
} as const

// Cache keys
export const CACHE_KEYS = {
  user: 'womanza_user',
  permissions: 'womanza_permissions',
  analytics: 'womanza_analytics',
  products: 'womanza_products',
  orders: 'womanza_orders',
  customers: 'womanza_customers',
} as const

// Local storage keys
export const STORAGE_KEYS = {
  theme: 'womanza-theme',
  sidebarCollapsed: 'womanza-sidebar-collapsed',
  tablePreferences: 'womanza-table-preferences',
  dashboardLayout: 'womanza-dashboard-layout',
} as const

// API endpoints (relative to Supabase base URL)
export const API_ENDPOINTS = {
  products: '/rest/v1/products',
  orders: '/rest/v1/orders',
  customers: '/rest/v1/customers',
  categories: '/rest/v1/categories',
  users: '/rest/v1/store_users',
  invites: '/rest/v1/user_invites',
  analytics: '/rest/v1/rpc/get_analytics',
} as const

// Email templates
export const EMAIL_TEMPLATES = {
  user_invite: {
    subject: 'You\'ve been invited to Womanza Admin Panel',
    template: 'user_invite',
  },
  password_reset: {
    subject: 'Reset your Womanza Admin Panel password',
    template: 'password_reset',
  },
  order_confirmation: {
    subject: 'Order Confirmation - Womanza Store',
    template: 'order_confirmation',
  },
} as const

// Feature flags
export const FEATURES = {
  enableAnalytics: true,
  enableMarketing: true,
  enableCustomerGroups: true,
  enableInventoryTracking: true,
  enableEmailCampaigns: true,
  enableAdvancedReporting: true,
  enableBulkOperations: true,
  enableExportImport: true,
} as const

// Time formats
export const TIME_FORMATS = {
  date: 'MMM dd, yyyy',
  datetime: 'MMM dd, yyyy HH:mm',
  time: 'HH:mm',
  dateInput: 'yyyy-MM-dd',
  datetimeInput: 'yyyy-MM-dd\'T\'HH:mm',
} as const

// Currency formatting
export const CURRENCY_FORMAT = {
  currency: 'USD',
  locale: 'en-US',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
} as const
