import React, { useState, useRef, useEffect } from 'react'
import {
  MoonIcon,
  SunIcon,
  BellIcon,
  MagnifyingGlassIcon,
  QuestionMarkCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { useTheme } from '@/contexts/theme-context'
import { useAuth } from '@/contexts/auth-context'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'
import { UserMenu } from '@/components/ui/user-menu'
import { GlobalSearch } from './global-search'
import { NotificationBell } from './notification-bell'
import { HelpButton } from './help-button'

export function Header() {
  const { theme, setTheme, actualTheme } = useTheme()
  const { user } = useAuth()
  const { currentStore } = useStore()
  const [showGlobalSearch, setShowGlobalSearch] = useState(false)

  const toggleTheme = () => {
    setTheme(actualTheme === 'dark' ? 'light' : 'dark')
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showGlobalSearch) {
        setShowGlobalSearch(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showGlobalSearch])

  return (
    <header className="h-16 bg-admin-bg-secondary border-b border-admin-border relative z-50">
      <div className="flex items-center justify-between h-full px-6">
        {/* Left Section */}
        <div className="flex items-center space-x-6">
          {/* Global Search */}
          <div className="relative">
            <button
              onClick={() => setShowGlobalSearch(!showGlobalSearch)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
              <span className="hidden md:inline">Search...</span>
            </button>
            {showGlobalSearch && (
              <GlobalSearch onClose={() => setShowGlobalSearch(false)} />
            )}
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Help Button */}
          <HelpButton />

          {/* Notifications */}
          <NotificationBell />

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="p-2 hover:bg-admin-hover"
            title={`Switch to ${actualTheme === 'dark' ? 'light' : 'dark'} mode`}
          >
            {actualTheme === 'dark' ? (
              <SunIcon className="h-5 w-5" />
            ) : (
              <MoonIcon className="h-5 w-5" />
            )}
          </Button>

          {/* User Menu */}
          <UserMenu user={user} />
        </div>
      </div>
    </header>
  )
}
