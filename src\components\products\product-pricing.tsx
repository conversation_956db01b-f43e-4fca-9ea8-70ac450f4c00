import { UseFormReturn } from 'react-hook-form'
import type { ProductFormData } from '@/types/product'

interface ProductPricingProps {
  form: UseFormReturn<ProductFormData>
}

export function ProductPricing({ form }: ProductPricingProps) {
  const { register, formState: { errors }, watch } = form

  const price = watch('price')
  const compareAtPrice = watch('compare_at_price')
  const costPrice = watch('cost_price')

  const calculateMargin = () => {
    if (price && costPrice && costPrice > 0) {
      return (((price - costPrice) / price) * 100).toFixed(1)
    }
    return '0'
  }

  const calculateDiscount = () => {
    if (price && compareAtPrice && compareAtPrice > price) {
      return (((compareAtPrice - price) / compareAtPrice) * 100).toFixed(1)
    }
    return '0'
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          Pricing Information
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Set the pricing details for your product including regular price, compare price, and cost.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Regular Price */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Regular Price *
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary">
              $
            </span>
            <input
              type="number"
              step="0.01"
              min="0"
              {...register('price', { valueAsNumber: true })}
              className="w-full pl-8 pr-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="0.00"
            />
          </div>
          {errors.price && (
            <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
          )}
          <p className="mt-1 text-xs text-admin-text-secondary">
            The price customers will pay for this product
          </p>
        </div>

        {/* Compare At Price */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Compare At Price
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary">
              $
            </span>
            <input
              type="number"
              step="0.01"
              min="0"
              {...register('compare_at_price', { valueAsNumber: true })}
              className="w-full pl-8 pr-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="0.00"
            />
          </div>
          <p className="mt-1 text-xs text-admin-text-secondary">
            Original price to show discount (optional)
          </p>
          {compareAtPrice && compareAtPrice > price && (
            <p className="mt-1 text-xs text-green-600">
              {calculateDiscount()}% discount will be shown
            </p>
          )}
        </div>

        {/* Cost Price */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Cost Price
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary">
              $
            </span>
            <input
              type="number"
              step="0.01"
              min="0"
              {...register('cost_price', { valueAsNumber: true })}
              className="w-full pl-8 pr-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="0.00"
            />
          </div>
          <p className="mt-1 text-xs text-admin-text-secondary">
            Your cost for this product (for profit calculations)
          </p>
        </div>

        {/* Profit Margin Display */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Profit Margin
          </label>
          <div className="px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary">
            <span className="text-lg font-semibold text-admin-text-primary">
              {calculateMargin()}%
            </span>
          </div>
          <p className="mt-1 text-xs text-admin-text-secondary">
            Calculated based on regular price and cost price
          </p>
        </div>
      </div>

      {/* Pricing Summary */}
      {price > 0 && (
        <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-4">
          <h4 className="text-sm font-medium text-admin-text-primary mb-3">
            Pricing Summary
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-admin-text-secondary">Customer Pays</p>
              <p className="text-lg font-semibold text-admin-text-primary">
                ${price?.toFixed(2) || '0.00'}
              </p>
            </div>
            
            {costPrice && costPrice > 0 && (
              <>
                <div>
                  <p className="text-admin-text-secondary">Your Cost</p>
                  <p className="text-lg font-semibold text-admin-text-primary">
                    ${costPrice.toFixed(2)}
                  </p>
                </div>
                
                <div>
                  <p className="text-admin-text-secondary">Profit per Sale</p>
                  <p className="text-lg font-semibold text-green-600">
                    ${(price - costPrice).toFixed(2)}
                  </p>
                </div>
              </>
            )}
          </div>

          {compareAtPrice && compareAtPrice > price && (
            <div className="mt-4 pt-4 border-t border-admin-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-admin-text-secondary">Original Price</p>
                  <p className="text-sm line-through text-admin-text-secondary">
                    ${compareAtPrice.toFixed(2)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-admin-text-secondary">Discount</p>
                  <p className="text-sm font-semibold text-red-600">
                    -{calculateDiscount()}%
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-admin-text-secondary">You Save</p>
                  <p className="text-sm font-semibold text-red-600">
                    ${(compareAtPrice - price).toFixed(2)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Pricing Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          💡 Pricing Tips
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Research competitor prices for similar jewelry pieces</li>
          <li>• Consider material costs, labor, and desired profit margin</li>
          <li>• Use compare-at price to highlight discounts and sales</li>
          <li>• Track cost price to monitor profitability over time</li>
          <li>• Consider psychological pricing (e.g., $99 vs $100)</li>
        </ul>
      </div>
    </div>
  )
}
