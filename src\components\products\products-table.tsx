import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  StarIcon,
  ExclamationTriangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'
import type { Product } from '@/types/product'

interface ProductsTableProps {
  products: Product[]
  isLoading: boolean
  error: any
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  onPageChange: (page: number) => void
  onRefresh: () => void
}

export function ProductsTable({ 
  products, 
  isLoading, 
  error, 
  pagination,
  onPageChange,
  onRefresh 
}: ProductsTableProps) {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusBadge = (status: Product['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      draft: { color: 'bg-yellow-100 text-yellow-800', label: 'Draft' },
      archived: { color: 'bg-gray-100 text-gray-800', label: 'Archived' },
    }

    const config = statusConfig[status] || statusConfig.draft

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  const getStockStatus = (product: Product) => {
    if (product.inventory_quantity === 0) {
      return (
        <span className="inline-flex items-center gap-1 text-red-600">
          <ExclamationTriangleIcon className="h-4 w-4" />
          Out of stock
        </span>
      )
    }
    
    if (product.inventory_quantity <= product.low_stock_threshold) {
      return (
        <span className="inline-flex items-center gap-1 text-orange-600">
          <ExclamationTriangleIcon className="h-4 w-4" />
          Low stock
        </span>
      )
    }

    return (
      <span className="text-green-600">
        {product.inventory_quantity} in stock
      </span>
    )
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId])
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId))
    }
  }

  const handleDelete = (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      console.log('Delete product:', productId)
      // TODO: Implement delete functionality
    }
  }

  const handleToggleFeatured = (productId: string, currentStatus: boolean) => {
    console.log('Toggle featured:', productId, !currentStatus)
    // TODO: Implement toggle featured functionality
  }

  if (error) {
    return (
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-8">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading products</p>
          <button
            onClick={onRefresh}
            className="px-4 py-2 bg-admin-primary text-white rounded-md hover:bg-admin-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-admin-bg-secondary border border-admin-border rounded-lg overflow-hidden">
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-admin-border">
          <thead className="bg-admin-bg-primary">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedProducts.length === products.length && products.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Stock
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-admin-bg-secondary divide-y divide-admin-border">
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <tr key={index}>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-4 w-4 bg-admin-border rounded"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse">
                      <div className="h-4 bg-admin-border rounded w-32 mb-2"></div>
                      <div className="h-3 bg-admin-border rounded w-24"></div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-6 bg-admin-border rounded w-16"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-4 bg-admin-border rounded w-20"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-4 bg-admin-border rounded w-24"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-4 bg-admin-border rounded w-20"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse h-4 bg-admin-border rounded w-20"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="animate-pulse flex gap-2 justify-end">
                      <div className="h-8 w-8 bg-admin-border rounded"></div>
                      <div className="h-8 w-8 bg-admin-border rounded"></div>
                      <div className="h-8 w-8 bg-admin-border rounded"></div>
                    </div>
                  </td>
                </tr>
              ))
            ) : products.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-12 text-center">
                  <div className="text-admin-text-secondary">
                    <p className="text-lg mb-2">No products found</p>
                    <p className="text-sm">Try adjusting your filters or create your first product</p>
                  </div>
                </td>
              </tr>
            ) : (
              products.map((product) => (
                <tr key={product.id} className="hover:bg-admin-hover">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                      className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        {product.images && product.images.length > 0 ? (
                          <img
                            className="h-12 w-12 rounded-lg object-cover"
                            src={product.images[0]}
                            alt={product.name}
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-lg bg-admin-border flex items-center justify-center">
                            <span className="text-admin-text-secondary text-xs">No image</span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center gap-2">
                          <div className="text-sm font-medium text-admin-text-primary">
                            {product.name}
                          </div>
                          {product.is_featured && (
                            <StarSolidIcon className="h-4 w-4 text-yellow-400" title="Featured" />
                          )}
                        </div>
                        <div className="text-sm text-admin-text-secondary">
                          SKU: {product.sku || 'N/A'}
                        </div>
                        {product.metal_type && (
                          <div className="text-xs text-admin-text-secondary">
                            {product.metal_type}
                            {product.gemstone_type && ` • ${product.gemstone_type}`}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(product.status)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-admin-text-primary">
                      {formatCurrency(product.price)}
                    </div>
                    {product.compare_at_price && product.compare_at_price > product.price && (
                      <div className="text-xs text-admin-text-secondary line-through">
                        {formatCurrency(product.compare_at_price)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      {getStockStatus(product)}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-admin-text-primary">
                      {product.category?.name || 'Uncategorized'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-admin-text-secondary">
                      {formatDate(product.created_at)}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Link
                        to={`/products/${product.id}`}
                        className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
                        title="View product"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Link>
                      
                      <Link
                        to={`/products/${product.id}/edit`}
                        className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
                        title="Edit product"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Link>

                      <button
                        onClick={() => handleToggleFeatured(product.id, product.is_featured)}
                        className={`p-2 hover:bg-admin-hover rounded-md transition-colors ${
                          product.is_featured 
                            ? 'text-yellow-500 hover:text-yellow-600' 
                            : 'text-admin-text-secondary hover:text-admin-text-primary'
                        }`}
                        title={product.is_featured ? 'Remove from featured' : 'Add to featured'}
                      >
                        <StarIcon className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleDelete(product.id)}
                        className="p-2 text-admin-text-secondary hover:text-red-600 hover:bg-admin-hover rounded-md transition-colors"
                        title="Delete product"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {!isLoading && products.length > 0 && (
        <div className="bg-admin-bg-primary px-6 py-3 border-t border-admin-border">
          <div className="flex items-center justify-between">
            <div className="text-sm text-admin-text-secondary">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} results
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => onPageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className="p-2 text-admin-text-secondary hover:text-admin-text-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <button
                      key={page}
                      onClick={() => onPageChange(page)}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        page === pagination.page
                          ? 'bg-admin-primary text-white'
                          : 'text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
              </div>

              <button
                onClick={() => onPageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
                className="p-2 text-admin-text-secondary hover:text-admin-text-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="bg-admin-primary text-white px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="text-sm">
              {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
            </div>
            <div className="flex items-center gap-3">
              <button className="text-sm hover:underline">
                Delete Selected
              </button>
              <button className="text-sm hover:underline">
                Update Status
              </button>
              <button className="text-sm hover:underline">
                Export Selected
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
