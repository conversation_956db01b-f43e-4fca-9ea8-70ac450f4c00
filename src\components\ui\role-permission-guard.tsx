import React from 'react'
import { useStore } from '@/contexts/store-context'
import { Permission, hasPermission, hasAnyPermission, hasAllPermissions, getRoleInfo } from '@/utils/role-permissions'
import { AdminRole } from '@/services/admin-user-service'

interface RolePermissionGuardProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  role?: AdminRole | AdminRole[]
  fallback?: React.ReactNode
  showFallback?: boolean
}

/**
 * Role-based Permission Guard Component
 */
export function RolePermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  role,
  fallback = null,
  showFallback = true
}: RolePermissionGuardProps) {
  const { getUserRole } = useStore()
  const currentRole = getUserRole()

  // Check role-based access
  if (role) {
    const allowedRoles = Array.isArray(role) ? role : [role]
    if (!currentRole || !allowedRoles.includes(currentRole)) {
      return showFallback ? <>{fallback}</> : null
    }
  }

  // Check permission-based access
  if (permission) {
    if (!hasPermission(currentRole, permission)) {
      return showFallback ? <>{fallback}</> : null
    }
  }

  // Check multiple permissions
  if (permissions.length > 0) {
    const hasAccess = requireAll 
      ? hasAllPermissions(currentRole, permissions)
      : hasAnyPermission(currentRole, permissions)
    
    if (!hasAccess) {
      return showFallback ? <>{fallback}</> : null
    }
  }

  return <>{children}</>
}

/**
 * Hook for role-based permissions
 */
export function useRolePermissions() {
  const { getUserRole } = useStore()
  const currentRole = getUserRole()

  return {
    role: currentRole,
    hasPermission: (permission: Permission) => hasPermission(currentRole, permission),
    hasAnyPermission: (permissions: Permission[]) => hasAnyPermission(currentRole, permissions),
    hasAllPermissions: (permissions: Permission[]) => hasAllPermissions(currentRole, permissions),
    canManageUsers: () => hasAnyPermission(currentRole, ['users.view', 'users.create', 'users.edit', 'users.delete']),
    canCreateProducts: () => hasPermission(currentRole, 'products.create'),
    canEditProducts: () => hasPermission(currentRole, 'products.edit'),
    canDeleteProducts: () => hasPermission(currentRole, 'products.delete'),
    canManageOrders: () => hasAnyPermission(currentRole, ['orders.edit', 'orders.fulfill']),
    canViewAnalytics: () => hasPermission(currentRole, 'analytics.view'),
    canEditSettings: () => hasPermission(currentRole, 'settings.edit'),
    isSuperAdmin: () => currentRole === 'super_admin',
    isAdmin: () => currentRole === 'admin',
    isEditor: () => currentRole === 'editor',
    isViewer: () => currentRole === 'viewer'
  }
}

/**
 * Access Denied Component
 */
interface AccessDeniedProps {
  title?: string
  message?: string
  showContactAdmin?: boolean
}

export function AccessDenied({
  title = "Access Denied",
  message = "You don't have permission to access this resource.",
  showContactAdmin = true
}: AccessDeniedProps) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto h-12 w-12 text-red-400 mb-4 text-4xl">
        🚫
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {title}
      </h3>
      <p className="text-gray-500 dark:text-gray-400 mb-4">
        {message}
      </p>
      {showContactAdmin && (
        <p className="text-sm text-gray-400 dark:text-gray-500">
          Contact your administrator if you believe this is an error.
        </p>
      )}
    </div>
  )
}

/**
 * Role Badge Component
 */
interface RoleBadgeProps {
  role: AdminRole
  showDescription?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function RoleBadge({ role, showDescription = false, size = 'md' }: RoleBadgeProps) {
  const roleStyles = {
    super_admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    admin: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    editor: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    viewer: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }

  const sizeStyles = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-1.5 text-sm',
    lg: 'px-3 py-2 text-base'
  }

  const info = getRoleInfo(role)

  return (
    <div className="flex items-center gap-2">
      <span className={`inline-flex items-center font-semibold rounded-full ${roleStyles[role]} ${sizeStyles[size]}`}>
        <span className="mr-1">{info.badge}</span>
        {info.name}
      </span>
      {showDescription && (
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {info.description}
        </span>
      )}
    </div>
  )
}

/**
 * Permission Button Component
 */
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  role?: AdminRole | AdminRole[]
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function PermissionButton({
  permission,
  permissions,
  requireAll,
  role,
  children,
  fallback = null,
  ...buttonProps
}: PermissionButtonProps) {
  return (
    <RolePermissionGuard
      permission={permission}
      permissions={permissions}
      requireAll={requireAll}
      role={role}
      fallback={fallback}
    >
      <button {...buttonProps}>
        {children}
      </button>
    </RolePermissionGuard>
  )
}

/**
 * Feature Flag Component - Shows/hides features based on role
 */
interface FeatureFlagProps {
  feature: 'user_management' | 'advanced_analytics' | 'system_settings' | 'bulk_operations'
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function FeatureFlag({ feature, children, fallback = null }: FeatureFlagProps) {
  const { role } = useRolePermissions()

  const featurePermissions = {
    user_management: ['users.view'] as Permission[],
    advanced_analytics: ['analytics.view', 'reports.export'] as Permission[],
    system_settings: ['settings.edit'] as Permission[],
    bulk_operations: ['products.edit', 'orders.edit'] as Permission[]
  }

  const requiredPermissions = featurePermissions[feature]

  return (
    <RolePermissionGuard
      permissions={requiredPermissions}
      requireAll={true}
      fallback={fallback}
    >
      {children}
    </RolePermissionGuard>
  )
}

/**
 * Role-based Navigation Item Component
 */
interface NavItemProps {
  permission?: Permission
  permissions?: Permission[]
  role?: AdminRole | AdminRole[]
  children: React.ReactNode
  className?: string
  onClick?: () => void
}

export function RoleNavItem({
  permission,
  permissions,
  role,
  children,
  className = '',
  onClick
}: NavItemProps) {
  return (
    <RolePermissionGuard
      permission={permission}
      permissions={permissions}
      role={role}
      showFallback={false}
    >
      <div className={className} onClick={onClick}>
        {children}
      </div>
    </RolePermissionGuard>
  )
}

/**
 * Quick Permission Check Component
 */
interface QuickCheckProps {
  check: 'canManageUsers' | 'canCreateProducts' | 'canViewAnalytics' | 'canEditSettings'
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function QuickCheck({ check, children, fallback = null }: QuickCheckProps) {
  const permissions = useRolePermissions()
  const hasAccess = permissions[check]()

  return hasAccess ? <>{children}</> : <>{fallback}</>
}
