# Womanza Admin Panel Environment Configuration
# Copy this file to .env and update the values

# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Application Configuration
VITE_APP_NAME=Womanza Admin Panel
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Store Configuration (Single Store Mode)
VITE_STORE_ID=womanza-store
VITE_STORE_NAME=Womanza Store
VITE_STORE_DOMAIN=womanza.com

# API Configuration (Optional)
# VITE_API_BASE_URL=https://api.womanza.com

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_USER_INVITES=true
VITE_ENABLE_EMAIL_CAMPAIGNS=true
VITE_ENABLE_ADVANCED_REPORTING=true

# Security Configuration
VITE_SESSION_TIMEOUT=3600000
VITE_INVITE_EXPIRY_DAYS=7

# Email Service Configuration (Optional - only needed for invitation system)
# Note: Current system uses direct user creation, no email required
# VITE_RESEND_API_KEY=your_resend_api_key
# VITE_EMAILJS_SERVICE_ID=your_emailjs_service_id
# VITE_EMAILJS_TEMPLATE_ID=your_emailjs_template_id
# VITE_EMAILJS_PUBLIC_KEY=your_emailjs_public_key

# Development Configuration
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

# Deployment Configuration (for production)
# Set these in your deployment platform (Vercel, Netlify, etc.)
# VITE_APP_ENVIRONMENT=production
# VITE_DEBUG_MODE=false
# VITE_LOG_LEVEL=warn
