import React, { useState, useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline'
import { UserService } from '@/services/user-service'
import { supabase } from '@/lib/supabase'
import { getRoleDisplayName } from '@/types/user'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import toast from 'react-hot-toast'

const acceptInviteSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type AcceptInviteFormData = z.infer<typeof acceptInviteSchema>

interface AcceptInviteFormProps {
  token: string
}

export function AcceptInviteForm({ token }: AcceptInviteFormProps) {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [inviteInfo, setInviteInfo] = useState<any>(null)
  const [inviteError, setInviteError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AcceptInviteFormData>({
    resolver: zodResolver(acceptInviteSchema),
  })

  useEffect(() => {
    // Validate token on component mount
    validateToken()
  }, [token])

  const validateToken = async () => {
    try {
      // Get invitation details directly from Supabase
      const { data: invite, error } = await supabase
        .from('user_invites')
        .select(`
          id,
          email,
          role,
          expires_at,
          stores:store_id (name)
        `)
        .eq('token', token)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single()

      if (error || !invite) {
        setInviteError('Invalid or expired invitation token')
        return
      }

      setInviteInfo({
        email: invite.email,
        role: invite.role,
        storeName: invite.stores?.name || 'Unknown Store',
        expiresAt: invite.expires_at
      })
    } catch (error) {
      console.error('Error validating token:', error)
      setInviteError('Failed to validate invitation')
    }
  }

  const onSubmit = async (data: AcceptInviteFormData) => {
    if (!inviteInfo) return

    setLoading(true)
    try {
      // First, sign up the user
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: inviteInfo.email,
        password: data.password,
        options: {
          data: {
            invitation_token: token
          }
        }
      })

      if (signUpError) throw signUpError

      if (authData.user && authData.session) {
        // User is signed in, now accept the invitation
        const { data: result, error: acceptError } = await supabase
          .rpc('accept_invitation', { invitation_token: token })

        if (acceptError) throw acceptError

        if (!result.success) {
          throw new Error(result.error)
        }

        toast.success('Account created and invitation accepted successfully!')
        navigate({ to: '/dashboard' })
      } else {
        // Email confirmation required
        toast.success('Please check your email to confirm your account, then return to complete the invitation.')
      }
    } catch (error: any) {
      console.error('Error accepting invitation:', error)
      toast.error(error.message || 'Failed to accept invitation')
    } finally {
      setLoading(false)
    }
  }

  if (inviteError) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-8 text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
          <h2 className="text-xl font-bold text-admin-text-primary mb-2">
            Invalid Invitation
          </h2>
          <p className="text-admin-text-muted mb-6">
            {inviteError}
          </p>
          <Button
            onClick={() => navigate({ to: '/login' })}
            variant="ghost"
          >
            Go to Login
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="h-12 w-12 bg-admin-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-lg">W</span>
          </div>
          <h2 className="text-2xl font-bold text-admin-text-primary mb-2">
            Welcome to Womanza
          </h2>
          <p className="text-admin-text-muted">
            Complete your account setup to get started
          </p>
        </div>

        {/* Invite Info */}
        {inviteInfo && (
          <div className="bg-admin-bg-primary rounded-lg p-4 mb-6 border border-admin-border">
            <div className="flex items-center gap-3">
              <CheckCircleIcon className="h-5 w-5 text-admin-success flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-admin-text-primary">
                  You've been invited as {getRoleDisplayName(inviteInfo.role)}
                </p>
                <p className="text-xs text-admin-text-muted">
                  Email: {inviteInfo.email}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Password Field */}
          <div>
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                {...register('password')}
                error={errors.password?.message}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-muted hover:text-admin-text-primary"
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          {/* Confirm Password Field */}
          <div>
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your password"
                {...register('confirmPassword')}
                error={errors.confirmPassword?.message}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-muted hover:text-admin-text-primary"
              >
                {showConfirmPassword ? (
                  <EyeSlashIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Account...
              </>
            ) : (
              'Create Account'
            )}
          </Button>
        </form>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-admin-text-muted">
            Already have an account?{' '}
            <button
              onClick={() => navigate({ to: '/login' })}
              className="text-admin-primary hover:underline"
            >
              Sign in
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
