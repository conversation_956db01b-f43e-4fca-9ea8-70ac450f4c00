import React, { useState } from 'react'
import {
  CogIcon,
  CreditCardIcon,
  TruckIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BellIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import toast from 'react-hot-toast'

export function SettingsContent() {
  const { hasPermission, currentStore } = useStore()
  const [activeTab, setActiveTab] = useState<'store' | 'payments' | 'shipping' | 'seo'>('store')
  const [loading, setLoading] = useState(false)

  // Store settings state
  const [storeSettings, setStoreSettings] = useState({
    name: currentStore.name,
    domain: currentStore.domain,
    description: currentStore.description || '',
    contact_email: currentStore.contact_email || '',
    contact_phone: currentStore.contact_phone || '',
    currency: currentStore.currency || 'USD',
    timezone: currentStore.settings?.timezone || 'America/New_York',
    language: currentStore.settings?.language || 'en',
    country: currentStore.settings?.country || 'US',
  })

  // Payment settings state
  const [paymentSettings, setPaymentSettings] = useState({
    stripe_publishable_key: '',
    stripe_secret_key: '',
    paypal_client_id: '',
    paypal_client_secret: '',
    enable_stripe: true,
    enable_paypal: false,
  })

  // Shipping settings state
  const [shippingSettings, setShippingSettings] = useState({
    free_shipping_threshold: 100,
    domestic_shipping_rate: 9.99,
    international_shipping_rate: 24.99,
    processing_time: '1-2 business days',
    shipping_zones: [
      { name: 'Domestic', countries: ['US'], rate: 9.99 },
      { name: 'International', countries: ['*'], rate: 24.99 },
    ],
  })

  // SEO settings state
  const [seoSettings, setSeoSettings] = useState({
    meta_title: 'Womanza Store - Premium Jewelry & Accessories',
    meta_description: 'Discover timeless elegance with our premium jewelry collection. Shop rings, necklaces, earrings, and more.',
    meta_keywords: 'jewelry, accessories, rings, necklaces, earrings, premium, womanza',
    og_title: 'Womanza Store - Premium Jewelry & Accessories',
    og_description: 'Discover timeless elegance with our premium jewelry collection.',
    og_image: '',
    google_analytics_id: '',
    facebook_pixel_id: '',
  })

  const handleSave = async (section: string) => {
    const permissionMap = {
      store: 'manage_store_settings',
      payments: 'manage_payment_settings',
      shipping: 'manage_shipping_settings',
      seo: 'manage_seo_settings',
    }

    if (!hasPermission(permissionMap[section as keyof typeof permissionMap])) {
      toast.error(`You do not have permission to update ${section} settings`)
      return
    }

    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success(`${section.charAt(0).toUpperCase() + section.slice(1)} settings updated successfully!`)
    } catch (error) {
      toast.error(`Failed to update ${section} settings`)
    } finally {
      setLoading(false)
    }
  }

  const tabs = [
    { id: 'store', label: 'Store', icon: CogIcon, permission: 'manage_store_settings' },
    { id: 'payments', label: 'Payments', icon: CreditCardIcon, permission: 'manage_payment_settings' },
    { id: 'shipping', label: 'Shipping', icon: TruckIcon, permission: 'manage_shipping_settings' },
    { id: 'seo', label: 'SEO', icon: GlobeAltIcon, permission: 'manage_seo_settings' },
  ]

  const visibleTabs = tabs.filter(tab => hasPermission(tab.permission))

  if (visibleTabs.length === 0) {
    return (
      <div className="text-center py-12">
        <ShieldCheckIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
        <h3 className="text-lg font-medium text-admin-text-primary mb-2">
          Access Denied
        </h3>
        <p className="text-admin-text-muted">
          You don't have permission to access any settings.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-admin-text-primary">
          Settings
        </h1>
        <p className="text-admin-text-muted mt-1">
          Configure your store settings and integrations
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-admin-border">
        <nav className="-mb-px flex space-x-8">
          {visibleTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === tab.id
                  ? 'border-admin-primary text-admin-primary'
                  : 'border-transparent text-admin-text-muted hover:text-admin-text-secondary hover:border-admin-border'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Store Settings Tab */}
      {activeTab === 'store' && hasPermission('manage_store_settings') && (
        <div className="max-w-2xl">
          <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
            <h3 className="text-lg font-medium text-admin-text-primary mb-6">
              Store Information
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="store_name">Store Name</Label>
                  <Input
                    id="store_name"
                    value={storeSettings.name}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="domain">Domain</Label>
                  <Input
                    id="domain"
                    value={storeSettings.domain}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, domain: e.target.value }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <textarea
                  id="description"
                  value={storeSettings.description}
                  onChange={(e) => setStoreSettings(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contact_email">Contact Email</Label>
                  <Input
                    id="contact_email"
                    type="email"
                    value={storeSettings.contact_email}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, contact_email: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="contact_phone">Contact Phone</Label>
                  <Input
                    id="contact_phone"
                    value={storeSettings.contact_phone}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, contact_phone: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <select
                    id="currency"
                    value={storeSettings.currency}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, currency: e.target.value }))}
                    className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <select
                    id="timezone"
                    value={storeSettings.timezone}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, timezone: e.target.value }))}
                    className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                  >
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <select
                    id="language"
                    value={storeSettings.language}
                    onChange={(e) => setStoreSettings(prev => ({ ...prev, language: e.target.value }))}
                    className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => handleSave('store')}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Store Settings'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Settings Tab */}
      {activeTab === 'payments' && hasPermission('manage_payment_settings') && (
        <div className="max-w-2xl">
          <div className="space-y-6">
            <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
              <h3 className="text-lg font-medium text-admin-text-primary mb-6">
                Stripe Configuration
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="enable_stripe"
                    checked={paymentSettings.enable_stripe}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, enable_stripe: e.target.checked }))}
                    className="rounded border-admin-border"
                  />
                  <Label htmlFor="enable_stripe">Enable Stripe Payments</Label>
                </div>
                <div>
                  <Label htmlFor="stripe_publishable_key">Publishable Key</Label>
                  <Input
                    id="stripe_publishable_key"
                    type="password"
                    value={paymentSettings.stripe_publishable_key}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, stripe_publishable_key: e.target.value }))}
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <Label htmlFor="stripe_secret_key">Secret Key</Label>
                  <Input
                    id="stripe_secret_key"
                    type="password"
                    value={paymentSettings.stripe_secret_key}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, stripe_secret_key: e.target.value }))}
                    placeholder="sk_test_..."
                  />
                </div>
              </div>
            </div>

            <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
              <h3 className="text-lg font-medium text-admin-text-primary mb-6">
                PayPal Configuration
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="enable_paypal"
                    checked={paymentSettings.enable_paypal}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, enable_paypal: e.target.checked }))}
                    className="rounded border-admin-border"
                  />
                  <Label htmlFor="enable_paypal">Enable PayPal Payments</Label>
                </div>
                <div>
                  <Label htmlFor="paypal_client_id">Client ID</Label>
                  <Input
                    id="paypal_client_id"
                    type="password"
                    value={paymentSettings.paypal_client_id}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, paypal_client_id: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="paypal_client_secret">Client Secret</Label>
                  <Input
                    id="paypal_client_secret"
                    type="password"
                    value={paymentSettings.paypal_client_secret}
                    onChange={(e) => setPaymentSettings(prev => ({ ...prev, paypal_client_secret: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={() => handleSave('payments')}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Payment Settings'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Shipping Settings Tab */}
      {activeTab === 'shipping' && hasPermission('manage_shipping_settings') && (
        <div className="max-w-2xl">
          <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
            <h3 className="text-lg font-medium text-admin-text-primary mb-6">
              Shipping Configuration
            </h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="free_shipping_threshold">Free Shipping Threshold ($)</Label>
                <Input
                  id="free_shipping_threshold"
                  type="number"
                  value={shippingSettings.free_shipping_threshold}
                  onChange={(e) => setShippingSettings(prev => ({ ...prev, free_shipping_threshold: Number(e.target.value) }))}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="domestic_shipping_rate">Domestic Shipping Rate ($)</Label>
                  <Input
                    id="domestic_shipping_rate"
                    type="number"
                    step="0.01"
                    value={shippingSettings.domestic_shipping_rate}
                    onChange={(e) => setShippingSettings(prev => ({ ...prev, domestic_shipping_rate: Number(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="international_shipping_rate">International Shipping Rate ($)</Label>
                  <Input
                    id="international_shipping_rate"
                    type="number"
                    step="0.01"
                    value={shippingSettings.international_shipping_rate}
                    onChange={(e) => setShippingSettings(prev => ({ ...prev, international_shipping_rate: Number(e.target.value) }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="processing_time">Processing Time</Label>
                <Input
                  id="processing_time"
                  value={shippingSettings.processing_time}
                  onChange={(e) => setShippingSettings(prev => ({ ...prev, processing_time: e.target.value }))}
                />
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => handleSave('shipping')}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Shipping Settings'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* SEO Settings Tab */}
      {activeTab === 'seo' && hasPermission('manage_seo_settings') && (
        <div className="max-w-2xl">
          <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
            <h3 className="text-lg font-medium text-admin-text-primary mb-6">
              SEO & Analytics
            </h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="meta_title">Meta Title</Label>
                <Input
                  id="meta_title"
                  value={seoSettings.meta_title}
                  onChange={(e) => setSeoSettings(prev => ({ ...prev, meta_title: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="meta_description">Meta Description</Label>
                <textarea
                  id="meta_description"
                  value={seoSettings.meta_description}
                  onChange={(e) => setSeoSettings(prev => ({ ...prev, meta_description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                />
              </div>
              <div>
                <Label htmlFor="meta_keywords">Meta Keywords</Label>
                <Input
                  id="meta_keywords"
                  value={seoSettings.meta_keywords}
                  onChange={(e) => setSeoSettings(prev => ({ ...prev, meta_keywords: e.target.value }))}
                  placeholder="keyword1, keyword2, keyword3"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                  <Input
                    id="google_analytics_id"
                    value={seoSettings.google_analytics_id}
                    onChange={(e) => setSeoSettings(prev => ({ ...prev, google_analytics_id: e.target.value }))}
                    placeholder="GA-XXXXXXXXX-X"
                  />
                </div>
                <div>
                  <Label htmlFor="facebook_pixel_id">Facebook Pixel ID</Label>
                  <Input
                    id="facebook_pixel_id"
                    value={seoSettings.facebook_pixel_id}
                    onChange={(e) => setSeoSettings(prev => ({ ...prev, facebook_pixel_id: e.target.value }))}
                    placeholder="123456789012345"
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <Button
                onClick={() => handleSave('seo')}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save SEO Settings'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
