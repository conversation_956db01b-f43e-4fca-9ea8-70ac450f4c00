import { createClient } from '@supabase/supabase-js'
import { env } from './env'

export const supabase = createClient(
  env.VITE_SUPABASE_URL,
  env.VITE_SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      storage: window.localStorage,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
)

// Database types (to be generated from Supabase CLI)
export type Database = {
  public: {
    Tables: {
      stores: {
        Row: {
          id: string
          name: string
          domain: string
          settings: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          domain: string
          settings?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          domain?: string
          settings?: Record<string, any>
          updated_at?: string
        }
      }
      store_users: {
        Row: {
          id: string
          store_id: string
          user_id: string
          role: 'owner' | 'admin' | 'manager' | 'staff'
          permissions: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          store_id: string
          user_id: string
          role: 'owner' | 'admin' | 'manager' | 'staff'
          permissions?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          store_id?: string
          user_id?: string
          role?: 'owner' | 'admin' | 'manager' | 'staff'
          permissions?: string[]
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          store_id: string
          name: string
          description: string
          price: number
          category_id: string
          images: string[]
          inventory: number
          status: 'active' | 'inactive' | 'draft'
          jewelry_details: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          store_id: string
          name: string
          description?: string
          price: number
          category_id: string
          images?: string[]
          inventory?: number
          status?: 'active' | 'inactive' | 'draft'
          jewelry_details?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          store_id?: string
          name?: string
          description?: string
          price?: number
          category_id?: string
          images?: string[]
          inventory?: number
          status?: 'active' | 'inactive' | 'draft'
          jewelry_details?: Record<string, any>
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          store_id: string
          customer_id: string
          status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total: number
          items: Record<string, any>[]
          shipping_address: Record<string, any>
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          store_id: string
          customer_id: string
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total: number
          items: Record<string, any>[]
          shipping_address: Record<string, any>
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          store_id?: string
          customer_id?: string
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          total?: number
          items?: Record<string, any>[]
          shipping_address?: Record<string, any>
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
