import { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import { 
  PhotoIcon, 
  XMarkIcon, 
  ArrowUpTrayIcon,
  EyeIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import type { ProductFormData } from '@/types/product'

interface ProductImagesProps {
  form: UseFormReturn<ProductFormData>
}

export function ProductImages({ form }: ProductImagesProps) {
  const { watch, setValue } = form
  const [dragOver, setDragOver] = useState(false)
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  const images = watch('images') || []
  const videos = watch('videos') || []

  const handleImageUpload = (files: FileList | null) => {
    if (!files) return

    // In a real implementation, you would upload files to your storage service
    // For now, we'll simulate with placeholder URLs
    const newImages = Array.from(files).map((file, index) => 
      `https://via.placeholder.com/400x400?text=Image+${images.length + index + 1}`
    )

    setValue('images', [...images, ...newImages])
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleImageUpload(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    setValue('images', newImages)
  }

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images]
    const [movedImage] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, movedImage)
    setValue('images', newImages)
  }

  const addVideoUrl = () => {
    const url = prompt('Enter video URL (YouTube, Vimeo, etc.):')
    if (url) {
      setValue('videos', [...videos, url])
    }
  }

  const removeVideo = (index: number) => {
    const newVideos = videos.filter((_, i) => i !== index)
    setValue('videos', newVideos)
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          Images & Media
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Upload high-quality images and videos to showcase your jewelry piece. The first image will be used as the main product image.
        </p>
      </div>

      {/* Image Upload Area */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-admin-text-primary">
          Product Images
        </h4>

        {/* Upload Zone */}
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragOver
              ? 'border-admin-primary bg-admin-primary/5'
              : 'border-admin-border hover:border-admin-primary/50'
          }`}
        >
          <PhotoIcon className="mx-auto h-12 w-12 text-admin-text-secondary" />
          <div className="mt-4">
            <label className="cursor-pointer">
              <span className="text-sm font-medium text-admin-primary hover:text-admin-primary/80">
                Upload images
              </span>
              <input
                type="file"
                multiple
                accept="image/*"
                className="hidden"
                onChange={(e) => handleImageUpload(e.target.files)}
              />
            </label>
            <span className="text-sm text-admin-text-secondary"> or drag and drop</span>
          </div>
          <p className="text-xs text-admin-text-secondary mt-2">
            PNG, JPG, GIF up to 10MB each
          </p>
        </div>

        {/* Image Grid */}
        {images.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div
                key={index}
                className="relative group bg-admin-bg-primary border border-admin-border rounded-lg overflow-hidden"
              >
                <img
                  src={image}
                  alt={`Product image ${index + 1}`}
                  className="w-full h-32 object-cover"
                />
                
                {/* Main Image Badge */}
                {index === 0 && (
                  <div className="absolute top-2 left-2 bg-admin-primary text-white text-xs px-2 py-1 rounded">
                    Main
                  </div>
                )}

                {/* Image Actions */}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <button
                    type="button"
                    onClick={() => setPreviewImage(image)}
                    className="p-2 bg-white text-gray-700 rounded-md hover:bg-gray-100"
                    title="Preview"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index - 1)}
                      className="p-2 bg-white text-gray-700 rounded-md hover:bg-gray-100"
                      title="Move left"
                    >
                      ←
                    </button>
                  )}
                  
                  {index < images.length - 1 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index + 1)}
                      className="p-2 bg-white text-gray-700 rounded-md hover:bg-gray-100"
                      title="Move right"
                    >
                      →
                    </button>
                  )}
                  
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="p-2 bg-white text-red-600 rounded-md hover:bg-gray-100"
                    title="Remove"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Video Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-admin-text-primary">
            Product Videos
          </h4>
          <button
            type="button"
            onClick={addVideoUrl}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-admin-primary text-white rounded-md hover:bg-admin-primary/90"
          >
            <PlusIcon className="h-4 w-4" />
            Add Video URL
          </button>
        </div>

        {videos.length > 0 ? (
          <div className="space-y-3">
            {videos.map((video, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-3 bg-admin-bg-primary border border-admin-border rounded-lg"
              >
                <div className="flex-1">
                  <p className="text-sm text-admin-text-primary truncate">
                    {video}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => removeVideo(index)}
                  className="p-1 text-red-600 hover:bg-red-50 rounded"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-admin-text-secondary">
            <p className="text-sm">No videos added yet</p>
            <p className="text-xs mt-1">Add video URLs to showcase your product in action</p>
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-full p-4">
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute top-4 right-4 p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
            <img
              src={previewImage}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>
      )}

      {/* Media Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          📸 Media Best Practices
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use high-resolution images (at least 1000x1000 pixels)</li>
          <li>• Show multiple angles and close-up details</li>
          <li>• Include lifestyle shots showing the jewelry being worn</li>
          <li>• Ensure consistent lighting and background</li>
          <li>• The first image will be the main product thumbnail</li>
          <li>• Videos can significantly increase conversion rates</li>
        </ul>
      </div>
    </div>
  )
}
