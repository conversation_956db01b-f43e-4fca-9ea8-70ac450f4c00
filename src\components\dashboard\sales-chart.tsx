import React, { useState } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'
import { ChevronDownIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import { useSalesData, useRefreshAnalytics } from '@/hooks/use-analytics'

const periodOptions = [
  { value: 7, label: '7 days' },
  { value: 30, label: '30 days' },
  { value: 90, label: '90 days' },
]

export function SalesChart() {
  const [selectedPeriod, setSelectedPeriod] = useState(30)
  const [showPeriodDropdown, setShowPeriodDropdown] = useState(false)
  const { data: salesData, isLoading, error } = useSalesData(selectedPeriod)
  const refreshAnalytics = useRefreshAnalytics()

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-3 shadow-lg">
          <p className="text-admin-text-primary font-medium">{label}</p>
          <p className="text-admin-primary">
            Sales: {formatCurrency(payload[0].value)}
          </p>
        </div>
      )
    }
    return null
  }

  if (error) {
    return (
      <div className="admin-card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
            Sales Overview
          </h3>
        </div>
        <div className="h-80 flex items-center justify-center">
          <div className="text-center text-admin-error">
            <p>Error loading sales data</p>
            <p className="text-sm text-admin-text-muted mt-1">
              {error instanceof Error ? error.message : 'Unknown error'}
            </p>
            <button
              onClick={refreshAnalytics}
              className="mt-2 text-admin-primary hover:text-admin-primary/80 text-sm"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
          Sales Overview
        </h3>
        <div className="flex items-center gap-2">
          <button
            onClick={refreshAnalytics}
            className="p-1 text-admin-text-muted hover:text-admin-text-primary transition-colors"
            title="Refresh data"
          >
            <ArrowPathIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <div className="relative">
            <button
              onClick={() => setShowPeriodDropdown(!showPeriodDropdown)}
              className="flex items-center gap-1 px-3 py-1 text-sm text-admin-text-secondary hover:text-admin-text-primary border border-admin-border rounded-md"
            >
              {periodOptions.find(p => p.value === selectedPeriod)?.label}
              <ChevronDownIcon className="h-4 w-4" />
            </button>
            {showPeriodDropdown && (
              <div className="absolute right-0 mt-1 bg-admin-bg-secondary border border-admin-border rounded-md shadow-lg z-10">
                {periodOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      setSelectedPeriod(option.value)
                      setShowPeriodDropdown(false)
                    }}
                    className="block w-full px-3 py-2 text-left text-sm text-admin-text-primary hover:bg-admin-hover"
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="h-80">
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-admin-primary mx-auto mb-2"></div>
              <p className="text-admin-text-muted text-sm">Loading sales data...</p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={salesData || []}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--admin-border)" />
              <XAxis
                dataKey="name"
                stroke="var(--admin-text-muted)"
                fontSize={12}
              />
              <YAxis
                stroke="var(--admin-text-muted)"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="sales"
                stroke="var(--admin-primary)"
                strokeWidth={2}
                dot={{ fill: 'var(--admin-primary)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--admin-primary)', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        )}
      </div>
    </div>
  )
}
