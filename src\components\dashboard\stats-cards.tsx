import React from 'react'
import {
  C<PERSON>rencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline'
import { useDashboardStats } from '@/hooks/use-analytics'

interface StatCardProps {
  name: string
  value: string
  change: number
  icon: React.ComponentType<{ className?: string }>
  loading?: boolean
}

function StatCard({ name, value, change, icon: Icon, loading }: StatCardProps) {
  const changeType = change >= 0 ? 'positive' : 'negative'
  const ChangeIcon = change >= 0 ? ArrowUpIcon : ArrowDownIcon

  if (loading) {
    return (
      <div className="admin-card p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-admin-bg-tertiary rounded animate-pulse" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <div className="space-y-2">
              <div className="h-4 bg-admin-bg-tertiary rounded animate-pulse" />
              <div className="h-6 bg-admin-bg-tertiary rounded animate-pulse w-3/4" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-card p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className="h-8 w-8 text-admin-primary" />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-admin-text-muted truncate">
              {name}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-admin-text-primary">
                {value}
              </div>
              <div
                className={`ml-2 flex items-center text-sm font-semibold ${
                  changeType === 'positive'
                    ? 'text-admin-success'
                    : 'text-admin-error'
                }`}
              >
                <ChangeIcon className="h-3 w-3 mr-1" />
                {Math.abs(change).toFixed(1)}%
              </div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  )
}

export function StatsCards() {
  const { data: stats, isLoading, error } = useDashboardStats()

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="admin-card p-6 col-span-full">
          <div className="text-center text-admin-error">
            <p>Error loading dashboard statistics</p>
            <p className="text-sm text-admin-text-muted mt-1">
              {error instanceof Error ? error.message : 'Unknown error'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        name="Total Revenue"
        value={stats ? formatCurrency(stats.totalRevenue) : '$0.00'}
        change={stats?.revenueChange || 0}
        icon={CurrencyDollarIcon}
        loading={isLoading}
      />
      <StatCard
        name="Orders"
        value={stats ? formatNumber(stats.totalOrders) : '0'}
        change={stats?.ordersChange || 0}
        icon={ShoppingBagIcon}
        loading={isLoading}
      />
      <StatCard
        name="Customers"
        value={stats ? formatNumber(stats.totalCustomers) : '0'}
        change={stats?.customersChange || 0}
        icon={UsersIcon}
        loading={isLoading}
      />
      <StatCard
        name="Conversion Rate"
        value={stats ? `${stats.conversionRate.toFixed(1)}%` : '0.0%'}
        change={stats?.conversionChange || 0}
        icon={ChartBarIcon}
        loading={isLoading}
      />
    </div>
  )
}
