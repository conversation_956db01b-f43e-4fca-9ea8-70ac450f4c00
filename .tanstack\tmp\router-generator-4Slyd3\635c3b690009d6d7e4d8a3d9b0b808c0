/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ProductsRouteImport } from './routes/products'
import { Route as LoginRouteImport } from './routes/login'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ProductsNewRouteImport } from './routes/products.new'
import { Route as ProductsInventoryRouteImport } from './routes/products.inventory'
import { Route as ProductsCategoriesRouteImport } from './routes/products.categories'

const ProductsRoute = ProductsRouteImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProductsNewRoute = ProductsNewRouteImport.update({
  id: '/new',
  path: '/new',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsInventoryRoute = ProductsInventoryRouteImport.update({
  id: '/inventory',
  path: '/inventory',
  getParentRoute: () => ProductsRoute,
} as any)
const ProductsCategoriesRoute = ProductsCategoriesRouteImport.update({
  id: '/categories',
  path: '/categories',
  getParentRoute: () => ProductsRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/products': typeof ProductsRouteWithChildren
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/products': typeof ProductsRouteWithChildren
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRoute
  '/login': typeof LoginRoute
  '/products': typeof ProductsRouteWithChildren
  '/products/categories': typeof ProductsCategoriesRoute
  '/products/inventory': typeof ProductsInventoryRoute
  '/products/new': typeof ProductsNewRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/login'
    | '/products'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/dashboard'
    | '/login'
    | '/products'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/login'
    | '/products'
    | '/products/categories'
    | '/products/inventory'
    | '/products/new'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRoute
  LoginRoute: typeof LoginRoute
  ProductsRoute: typeof ProductsRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/products': {
      id: '/products'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products/new': {
      id: '/products/new'
      path: '/new'
      fullPath: '/products/new'
      preLoaderRoute: typeof ProductsNewRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/inventory': {
      id: '/products/inventory'
      path: '/inventory'
      fullPath: '/products/inventory'
      preLoaderRoute: typeof ProductsInventoryRouteImport
      parentRoute: typeof ProductsRoute
    }
    '/products/categories': {
      id: '/products/categories'
      path: '/categories'
      fullPath: '/products/categories'
      preLoaderRoute: typeof ProductsCategoriesRouteImport
      parentRoute: typeof ProductsRoute
    }
  }
}

interface ProductsRouteChildren {
  ProductsCategoriesRoute: typeof ProductsCategoriesRoute
  ProductsInventoryRoute: typeof ProductsInventoryRoute
  ProductsNewRoute: typeof ProductsNewRoute
}

const ProductsRouteChildren: ProductsRouteChildren = {
  ProductsCategoriesRoute: ProductsCategoriesRoute,
  ProductsInventoryRoute: ProductsInventoryRoute,
  ProductsNewRoute: ProductsNewRoute,
}

const ProductsRouteWithChildren = ProductsRoute._addFileChildren(
  ProductsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRoute,
  LoginRoute: LoginRoute,
  ProductsRoute: ProductsRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
