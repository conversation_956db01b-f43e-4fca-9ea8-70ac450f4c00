import React from 'react'
import { ArrowPathIcon, ClockIcon } from '@heroicons/react/24/outline'
import { useRecentOrders, useRefreshAnalytics } from '@/hooks/use-analytics'

const statusColors = {
  pending: 'text-admin-warning bg-admin-warning/10',
  processing: 'text-admin-info bg-admin-info/10',
  shipped: 'text-admin-primary bg-admin-primary/10',
  delivered: 'text-admin-success bg-admin-success/10',
  cancelled: 'text-admin-error bg-admin-error/10',
}

const statusLabels = {
  pending: 'Pending',
  processing: 'Processing',
  shipped: 'Shipped',
  delivered: 'Delivered',
  cancelled: 'Cancelled',
}

export function RecentOrders() {
  const { data: orders, isLoading, error } = useRecentOrders(5)
  const refreshAnalytics = useRefreshAnalytics()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  if (error) {
    return (
      <div className="admin-card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
            Recent Orders
          </h3>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-center text-admin-error">
            <p>Error loading recent orders</p>
            <p className="text-sm text-admin-text-muted mt-1">
              {error instanceof Error ? error.message : 'Unknown error'}
            </p>
            <button
              onClick={refreshAnalytics}
              className="mt-2 text-admin-primary hover:text-admin-primary/80 text-sm"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
          Recent Orders
        </h3>
        <button
          onClick={refreshAnalytics}
          className="p-1 text-admin-text-muted hover:text-admin-text-primary transition-colors"
          title="Refresh orders"
        >
          <ArrowPathIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className="space-y-4">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="p-4 bg-admin-bg-tertiary rounded-lg animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-admin-border rounded w-1/3 mb-2"></div>
                  <div className="h-3 bg-admin-border rounded w-1/2"></div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-admin-border rounded w-16 mb-2"></div>
                  <div className="h-6 bg-admin-border rounded w-20"></div>
                </div>
              </div>
            </div>
          ))
        ) : orders && orders.length > 0 ? (
          orders.map((order) => (
            <div
              key={order.id}
              className="flex items-center justify-between p-4 bg-admin-bg-tertiary rounded-lg hover:bg-admin-hover transition-colors"
            >
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-admin-text-primary">
                      {order.customer_name}
                    </p>
                    <p className="text-xs text-admin-text-muted">
                      {order.id} • {order.product_name}
                    </p>
                    <div className="flex items-center mt-1 text-xs text-admin-text-muted">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      {formatDate(order.created_at)}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-admin-text-primary">
                      {formatCurrency(order.total)}
                    </p>
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        statusColors[order.status as keyof typeof statusColors] ||
                        'text-admin-text-muted bg-admin-bg-tertiary'
                      }`}
                    >
                      {statusLabels[order.status as keyof typeof statusLabels] || order.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-admin-text-muted">
            <p>No recent orders found</p>
            <p className="text-sm mt-1">Orders will appear here once customers start purchasing</p>
          </div>
        )}
      </div>

      {orders && orders.length > 0 && (
        <div className="mt-4">
          <button className="text-sm text-admin-primary hover:text-admin-primary/80 font-medium transition-colors">
            View all orders →
          </button>
        </div>
      )}
    </div>
  )
}
