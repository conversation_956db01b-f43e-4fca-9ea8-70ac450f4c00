-- 🔧 Fix User Details and Role Logic
-- Run this script in your Supabase SQL Editor

-- STEP 1: Check current users
SELECT 'Current users before fix:' as info;
SELECT su.id, su.user_id, au.email, su.role, su.created_at
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY su.created_at;

-- Check if users table exists
SELECT 'Checking if users table exists:' as info;
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'users'
) as users_table_exists;

-- STEP 2: Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- STEP 3: Fix the user details in users table
DO $$
DECLARE
    admin_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Find the admin user by email
    SELECT id INTO admin_user_id
    FROM auth.users
    WHERE email = '<EMAIL>'
    LIMIT 1;

    IF admin_user_id IS NOT NULL THEN
        -- Ensure user exists in users table with correct details
        INSERT INTO users (id, email, first_name, last_name, password_hash, created_at, updated_at)
        VALUES (
            admin_user_id,
            '<EMAIL>',
            'Usman',
            'Ali',
            'auth_user', -- Special marker for auth users
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            first_name = 'Usman',
            last_name = 'Ali',
            updated_at = NOW();

        -- Update role in store_users to admin (not super_admin)
        UPDATE store_users
        SET
            role = 'admin',
            updated_at = NOW()
        WHERE user_id = admin_user_id
        AND store_id = womanza_store_id;

        RAISE NOTICE 'Fixed user details for Usman Ali (admin role)';
    ELSE
        RAISE NOTICE 'Admin user not found with email: <EMAIL>';
    END IF;
END $$;

-- STEP 4: Ensure proper role constraints
-- Create a function to enforce role limits
CREATE OR REPLACE FUNCTION check_role_limits()
RETURNS TRIGGER AS $$
DECLARE
    store_id_val UUID;
    super_admin_count INTEGER;
    admin_count INTEGER;
BEGIN
    store_id_val := NEW.store_id;

    -- Count existing super admins for this store (excluding current record)
    SELECT COUNT(*) INTO super_admin_count
    FROM store_users
    WHERE store_id = store_id_val
    AND role = 'super_admin'
    AND user_id != NEW.user_id;

    -- Count existing admins for this store (excluding current record)
    SELECT COUNT(*) INTO admin_count
    FROM store_users
    WHERE store_id = store_id_val
    AND role = 'admin'
    AND user_id != NEW.user_id;

    -- Check role limits
    IF NEW.role = 'super_admin' AND super_admin_count >= 1 THEN
        RAISE EXCEPTION 'Only one Super Admin allowed per store';
    END IF;

    IF NEW.role = 'admin' AND admin_count >= 1 THEN
        RAISE EXCEPTION 'Only one Admin allowed per store';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS enforce_role_limits ON store_users;

-- Create trigger to enforce role limits
CREATE TRIGGER enforce_role_limits
    BEFORE INSERT OR UPDATE ON store_users
    FOR EACH ROW
    EXECUTE FUNCTION check_role_limits();

-- STEP 4: Set up proper super admin (store owner)
-- For now, we'<NAME_EMAIL> as the super admin
DO $$
DECLARE
    super_admin_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Check if super admin email exists in auth
    SELECT id INTO super_admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    IF super_admin_user_id IS NOT NULL THEN
        -- Ensure super admin exists in users table
        INSERT INTO users (id, email, first_name, last_name, created_at, updated_at)
        VALUES (
            super_admin_user_id,
            '<EMAIL>',
            'Womanza',
            'Owner',
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            first_name = 'Womanza',
            last_name = 'Owner',
            updated_at = NOW();
        
        -- Ensure super admin exists in store_users
        INSERT INTO store_users (id, user_id, store_id, role, created_at, updated_at)
        VALUES (
            gen_random_uuid(),
            super_admin_user_id,
            womanza_store_id,
            'super_admin',
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id, store_id) DO UPDATE SET
            role = 'super_admin',
            updated_at = NOW();
            
        RAISE NOTICE 'Super admin (store owner) ensured';
    ELSE
        RAISE NOTICE 'Super admin email not found in auth.users';
    END IF;
END $$;

-- STEP 5: Show final results
SELECT 'Users after fix:' as info;
SELECT su.id, su.user_id, au.email, u.first_name, u.last_name, su.role, su.created_at 
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
LEFT JOIN users u ON su.user_id = u.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY 
    CASE su.role 
        WHEN 'super_admin' THEN 1 
        WHEN 'admin' THEN 2 
        WHEN 'editor' THEN 3 
        WHEN 'viewer' THEN 4 
    END,
    su.created_at;

-- Success message
SELECT 'User details and role logic fixed!' as status;
