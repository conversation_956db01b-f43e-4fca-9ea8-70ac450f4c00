-- 🔧 Fix Duplicate Email Error
-- Run this script in your Supabase SQL Editor

-- STEP 1: Check current state of users table
SELECT 'Current users in users table:' as info;
SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at;

-- STEP 2: Check what's in auth.users for our target email
SELECT 'Auth user details:' as info;
SELECT id, email, created_at FROM auth.users WHERE email = '<EMAIL>';

-- STEP 3: Fix the duplicate email issue
DO $$
DECLARE
    admin_user_id UUID;
    existing_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Find the admin user by email in auth.users
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    -- Check if email already exists in users table and get its ID
    SELECT id INTO existing_user_id
    FROM users 
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF admin_user_id IS NOT NULL THEN
        IF existing_user_id IS NOT NULL THEN
            -- Email exists but might have different ID, update the existing record
            UPDATE users 
            SET 
                id = admin_user_id,
                first_name = 'Usman',
                last_name = 'Ali',
                updated_at = NOW()
            WHERE email = '<EMAIL>';
            
            RAISE NOTICE 'Updated existing user record for Usman Ali';
        ELSE
            -- Email doesn't exist, safe to insert
            INSERT INTO users (id, email, first_name, last_name, password_hash, created_at, updated_at)
            VALUES (
                admin_user_id,
                '<EMAIL>',
                'Usman',
                'Ali',
                'auth_user',
                NOW(),
                NOW()
            );
            
            RAISE NOTICE 'Created new user record for Usman Ali';
        END IF;
        
        -- Update role in store_users to admin (not super_admin)
        UPDATE store_users 
        SET 
            role = 'admin',
            updated_at = NOW()
        WHERE user_id = admin_user_id 
        AND store_id = womanza_store_id;
        
        RAISE NOTICE 'Fixed user details for Usman Ali (admin role)';
    ELSE
        RAISE NOTICE 'Admin user not found with email: <EMAIL>';
    END IF;
END $$;

-- STEP 4: Check final state
SELECT 'Users after fix:' as info;
SELECT su.id, su.user_id, au.email, u.first_name, u.last_name, su.role, su.created_at 
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
LEFT JOIN users u ON su.user_id = u.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY 
    CASE su.role 
        WHEN 'super_admin' THEN 1 
        WHEN 'admin' THEN 2 
        WHEN 'editor' THEN 3 
        WHEN 'viewer' THEN 4 
    END,
    su.created_at;

-- Success message
SELECT 'User details fixed successfully!' as status;
