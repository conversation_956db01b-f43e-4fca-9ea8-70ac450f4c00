import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { AnalyticsService, DashboardStats, SalesData, RecentOrder } from '@/services/analytics'
import { useStore } from '@/contexts/store-context'
import { env } from '@/lib/env'

export function useDashboardStats(period: 'day' | 'week' | 'month' = 'month') {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ['dashboard-stats', currentStore?.id, period],
    queryFn: () => {
      if (!currentStore?.id) {
        throw new Error('No store selected')
      }
      return AnalyticsService.getDashboardStats(currentStore.id, period)
    },
    enabled: !!currentStore?.id,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: env.VITE_ENABLE_REAL_TIME ? 1000 * 60 * 2 : false, // 2 minutes if real-time enabled
  })

  // Set up real-time subscriptions
  useEffect(() => {
    if (!currentStore?.id || !env.VITE_ENABLE_REAL_TIME) return

    const orderSubscription = AnalyticsService.subscribeToOrderUpdates(
      currentStore.id,
      () => {
        // Invalidate and refetch dashboard stats when orders change
        queryClient.invalidateQueries({ queryKey: ['dashboard-stats', currentStore.id] })
      }
    )

    const customerSubscription = AnalyticsService.subscribeToCustomerUpdates(
      currentStore.id,
      () => {
        // Invalidate and refetch dashboard stats when customers change
        queryClient.invalidateQueries({ queryKey: ['dashboard-stats', currentStore.id] })
      }
    )

    return () => {
      orderSubscription.unsubscribe()
      customerSubscription.unsubscribe()
    }
  }, [currentStore?.id, queryClient])

  return query
}

export function useSalesData(days: number = 30) {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ['sales-data', currentStore?.id, days],
    queryFn: () => {
      if (!currentStore?.id) {
        throw new Error('No store selected')
      }
      return AnalyticsService.getSalesData(currentStore.id, days)
    },
    enabled: !!currentStore?.id,
    staleTime: 1000 * 60 * 10, // 10 minutes
    refetchInterval: env.VITE_ENABLE_REAL_TIME ? 1000 * 60 * 5 : false, // 5 minutes if real-time enabled
  })

  // Set up real-time subscriptions for sales data
  useEffect(() => {
    if (!currentStore?.id || !env.VITE_ENABLE_REAL_TIME) return

    const subscription = AnalyticsService.subscribeToOrderUpdates(
      currentStore.id,
      () => {
        // Invalidate and refetch sales data when orders change
        queryClient.invalidateQueries({ queryKey: ['sales-data', currentStore.id] })
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [currentStore?.id, queryClient, days])

  return query
}

export function useRecentOrders(limit: number = 10) {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ['recent-orders', currentStore?.id, limit],
    queryFn: () => {
      if (!currentStore?.id) {
        throw new Error('No store selected')
      }
      return AnalyticsService.getRecentOrders(currentStore.id, limit)
    },
    enabled: !!currentStore?.id,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchInterval: env.VITE_ENABLE_REAL_TIME ? 1000 * 30 : false, // 30 seconds if real-time enabled
  })

  // Set up real-time subscriptions for recent orders
  useEffect(() => {
    if (!currentStore?.id || !env.VITE_ENABLE_REAL_TIME) return

    const subscription = AnalyticsService.subscribeToOrderUpdates(
      currentStore.id,
      (payload) => {
        // Invalidate and refetch recent orders when orders change
        queryClient.invalidateQueries({ queryKey: ['recent-orders', currentStore.id] })
        
        if (env.VITE_DEBUG_MODE) {
          console.log('Order update received:', payload)
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [currentStore?.id, queryClient, limit])

  return query
}

// Hook for invalidating all analytics data (useful for manual refresh)
export function useRefreshAnalytics() {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  return () => {
    if (currentStore?.id) {
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats', currentStore.id] })
      queryClient.invalidateQueries({ queryKey: ['sales-data', currentStore.id] })
      queryClient.invalidateQueries({ queryKey: ['recent-orders', currentStore.id] })
    }
  }
}

// Hook for getting loading states across all analytics queries
export function useAnalyticsLoading() {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  if (!currentStore?.id) return false

  const dashboardQuery = queryClient.getQueryState(['dashboard-stats', currentStore.id])
  const salesQuery = queryClient.getQueryState(['sales-data', currentStore.id])
  const ordersQuery = queryClient.getQueryState(['recent-orders', currentStore.id])

  return (
    dashboardQuery?.status === 'pending' ||
    salesQuery?.status === 'pending' ||
    ordersQuery?.status === 'pending'
  )
}

// Hook for getting error states across all analytics queries
export function useAnalyticsError() {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()

  if (!currentStore?.id) return null

  const dashboardQuery = queryClient.getQueryState(['dashboard-stats', currentStore.id])
  const salesQuery = queryClient.getQueryState(['sales-data', currentStore.id])
  const ordersQuery = queryClient.getQueryState(['recent-orders', currentStore.id])

  return (
    dashboardQuery?.error ||
    salesQuery?.error ||
    ordersQuery?.error
  )
}
