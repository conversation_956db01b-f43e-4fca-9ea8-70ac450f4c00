# 🚀 Quick Setup - User Management

## ✅ **Current Status**
- ✅ User management interface is working
- ✅ Super admin is displayed correctly
- ⚠️ User creation works with fallback storage (localStorage)
- 🔧 **For full functionality, run the database setup below**

## 🛠️ **Database Setup (Optional but Recommended)**

### **Step 1: Open Supabase Dashboard**
1. Go to your Supabase project dashboard
2. Click on "SQL Editor" in the left sidebar

### **Step 2: Run the Setup Script**
Copy and paste this SQL script:

```sql
-- Simple Users Table Setup for Womanza Admin Panel
-- Drop existing users table if it exists (for clean setup)
DROP TABLE IF EXISTS users CASCADE;

-- Create users table for storing user details
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table (store users can view users in their store)
DROP POLICY IF EXISTS "Store users can view users in their store" ON users;
CREATE POLICY "Store users can view users in their store" ON users
    FOR SELECT USING (
        id IN (
            SELECT su.user_id 
            FROM store_users su 
            WHERE su.store_id IN (
                SELECT store_id 
                FROM store_users 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create policy for inserting users (only super_admin and admin)
DROP POLICY IF EXISTS "Store admins can create users" ON users;
CREATE POLICY "Store admins can create users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 
            FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Create updated_at trigger for users
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Update store_users table to ensure it has proper structure
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE store_users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Create trigger for store_users updated_at
DROP TRIGGER IF EXISTS update_store_users_updated_at ON store_users;
CREATE TRIGGER update_store_users_updated_at 
    BEFORE UPDATE ON store_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

### **Step 3: Execute the Script**
1. Click "Run" button in Supabase SQL Editor
2. Wait for "Success" message
3. Refresh your admin panel

## 🧪 **Test User Creation**

### **Without Database Setup (Current):**
- User creation works with localStorage fallback
- Users will appear in the list
- Data is stored locally in browser

### **With Database Setup:**
- User creation works with full database integration
- Users are stored permanently in Supabase
- Better security and data persistence

## 🎯 **Test Steps**

1. **Go to User Management**: Navigate to `/users`
2. **Click "Add User"**: Use the blue "Add User" button
3. **Fill the Form**:
   - Email: `<EMAIL>`
   - First Name: `Test`
   - Last Name: `User`
   - Password: `password123`
   - Role: `Editor`
4. **Submit**: Click "Create User"
5. **Verify**: User should appear in the list

## ✅ **Expected Results**

- ✅ User appears in "All Users" list
- ✅ Correct role badge displayed
- ✅ Success toast notification
- ✅ No console errors

## 🔧 **Troubleshooting**

### **If user creation fails:**
1. Check browser console for errors
2. Verify you're logged in as Super Admin
3. Try refreshing the page
4. Run the database setup script if not done

### **If database setup fails:**
1. Check if you have proper Supabase permissions
2. Try running the script in smaller chunks
3. Contact support if issues persist

The system is designed to work both with and without the database setup! 🎉
