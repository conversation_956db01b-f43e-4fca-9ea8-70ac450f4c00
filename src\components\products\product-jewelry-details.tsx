import { UseFormReturn } from 'react-hook-form'
import type { ProductFormData } from '@/types/product'
import { METAL_TYPES, GEMSTONE_TYPES, METAL_PURITIES } from '@/types/product'

interface ProductJewelryDetailsProps {
  form: UseFormReturn<ProductFormData>
}

export function ProductJewelryDetails({ form }: ProductJewelryDetailsProps) {
  const { register, formState: { errors }, watch, setValue } = form

  const metalType = watch('metal_type')
  const gemstoneType = watch('gemstone_type')
  const dimensions = watch('dimensions') || {}

  const handleDimensionChange = (key: string, value: string) => {
    const newDimensions = { ...dimensions, [key]: value }
    setValue('dimensions', newDimensions)
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-admin-text-primary mb-4">
          Jewelry-Specific Details
        </h3>
        <p className="text-sm text-admin-text-secondary mb-6">
          Add detailed specifications for your jewelry piece including materials, gemstones, and physical properties.
        </p>
      </div>

      {/* Material Information */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          Material Information
        </h4>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Metal Type */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Metal Type
            </label>
            <select
              {...register('metal_type')}
              className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              <option value="">Select metal type</option>
              {METAL_TYPES.map((metal) => (
                <option key={metal} value={metal}>
                  {metal}
                </option>
              ))}
            </select>
          </div>

          {/* Metal Purity */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Metal Purity
            </label>
            <select
              {...register('metal_purity')}
              className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              <option value="">Select purity</option>
              {METAL_PURITIES.map((purity) => (
                <option key={purity} value={purity}>
                  {purity}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Gemstone Information */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          Gemstone Information
        </h4>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Gemstone Type */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Gemstone Type
            </label>
            <select
              {...register('gemstone_type')}
              className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              <option value="">Select gemstone</option>
              {GEMSTONE_TYPES.map((gemstone) => (
                <option key={gemstone} value={gemstone}>
                  {gemstone}
                </option>
              ))}
            </select>
          </div>

          {/* Gemstone Carat */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Gemstone Carat Weight
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.001"
                min="0"
                {...register('gemstone_carat', { valueAsNumber: true })}
                className="w-full px-3 py-2 pr-12 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0.000"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary text-sm">
                ct
              </span>
            </div>
            <p className="mt-1 text-xs text-admin-text-secondary">
              Total carat weight of all gemstones
            </p>
          </div>
        </div>
      </div>

      {/* Physical Properties */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          Physical Properties
        </h4>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weight */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Weight
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.01"
                min="0"
                {...register('weight_grams', { valueAsNumber: true })}
                className="w-full px-3 py-2 pr-12 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0.00"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary text-sm">
                g
              </span>
            </div>
            <p className="mt-1 text-xs text-admin-text-secondary">
              Total weight in grams
            </p>
          </div>

          {/* Ring Size (if applicable) */}
          <div>
            <label className="block text-sm font-medium text-admin-text-primary mb-2">
              Ring Size (if applicable)
            </label>
            <input
              type="text"
              value={dimensions.ring_size || ''}
              onChange={(e) => handleDimensionChange('ring_size', e.target.value)}
              className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="e.g., 7, 7.5, 8"
            />
          </div>
        </div>

        {/* Dimensions */}
        <div className="mt-6">
          <h5 className="text-sm font-medium text-admin-text-primary mb-3">
            Dimensions
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-xs font-medium text-admin-text-secondary mb-1">
                Length (mm)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                value={dimensions.length || ''}
                onChange={(e) => handleDimensionChange('length', e.target.value)}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0.0"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-admin-text-secondary mb-1">
                Width (mm)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                value={dimensions.width || ''}
                onChange={(e) => handleDimensionChange('width', e.target.value)}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0.0"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-admin-text-secondary mb-1">
                Height/Thickness (mm)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                value={dimensions.height || ''}
                onChange={(e) => handleDimensionChange('height', e.target.value)}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="0.0"
              />
            </div>
          </div>
        </div>

        {/* Chain Length (if applicable) */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Chain Length (if applicable)
          </label>
          <div className="relative">
            <input
              type="text"
              value={dimensions.chain_length || ''}
              onChange={(e) => handleDimensionChange('chain_length', e.target.value)}
              className="w-full px-3 py-2 pr-16 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
              placeholder="e.g., 16, 18, 20"
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary text-sm">
              inches
            </span>
          </div>
        </div>
      </div>

      {/* Care Instructions */}
      <div className="bg-admin-bg-primary border border-admin-border rounded-lg p-6">
        <h4 className="text-sm font-medium text-admin-text-primary mb-4">
          Care Instructions
        </h4>
        <textarea
          value={dimensions.care_instructions || ''}
          onChange={(e) => handleDimensionChange('care_instructions', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          placeholder="Enter care and maintenance instructions for this jewelry piece..."
        />
        <p className="mt-1 text-xs text-admin-text-secondary">
          Provide customers with proper care instructions to maintain the jewelry's quality
        </p>
      </div>

      {/* Jewelry Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          💎 Jewelry Specification Tips
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Accurate specifications help customers make informed decisions</li>
          <li>• Include gemstone certificates or authenticity details when available</li>
          <li>• Precise measurements are crucial for rings and fitted jewelry</li>
          <li>• Care instructions help maintain customer satisfaction</li>
          <li>• Consider adding origin information for precious stones</li>
        </ul>
      </div>
    </div>
  )
}
