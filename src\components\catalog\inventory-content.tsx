import React, { useState, useEffect } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  PencilIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'

interface InventoryItem {
  id: string
  name: string
  sku: string
  inventory_quantity: number
  low_stock_threshold: number
  cost_price: number
  price: number
  status: 'active' | 'draft' | 'archived'
  track_inventory: boolean
  category?: {
    name: string
  }
  updated_at: string
}

export function InventoryContent() {
  const { currentStore } = useStore()
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [stockFilter, setStockFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'quantity' | 'updated'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [editingQuantity, setEditingQuantity] = useState<string | null>(null)
  const [newQuantity, setNewQuantity] = useState<number>(0)

  useEffect(() => {
    loadInventory()
  }, [])

  const loadInventory = async () => {
    try {
      setLoading(true)
      // Sample inventory data for demonstration
      const sampleInventory: InventoryItem[] = [
        {
          id: '1',
          name: 'Gold Necklace Set',
          sku: 'GNS-001',
          category: { name: 'Necklaces' },
          price: 25000,
          cost_price: 20000,
          status: 'active',
          track_inventory: true,
          inventory_quantity: 15,
          low_stock_threshold: 5,
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          name: 'Diamond Earrings',
          sku: 'DE-002',
          category: { name: 'Earrings' },
          price: 45000,
          cost_price: 35000,
          status: 'active',
          track_inventory: true,
          inventory_quantity: 3,
          low_stock_threshold: 5,
          updated_at: '2024-01-14T15:45:00Z'
        },
        {
          id: '3',
          name: 'Silver Bracelet',
          sku: 'SB-003',
          category: { name: 'Bracelets' },
          price: 8000,
          cost_price: 6000,
          status: 'active',
          track_inventory: true,
          inventory_quantity: 0,
          low_stock_threshold: 3,
          updated_at: '2024-01-13T09:20:00Z'
        },
        {
          id: '4',
          name: 'Pearl Ring',
          sku: 'PR-004',
          category: { name: 'Rings' },
          price: 15000,
          cost_price: 12000,
          status: 'active',
          track_inventory: true,
          inventory_quantity: 25,
          low_stock_threshold: 8,
          updated_at: '2024-01-16T14:10:00Z'
        },
        {
          id: '5',
          name: 'Custom Pendant',
          sku: 'CP-005',
          category: { name: 'Pendants' },
          price: 18000,
          cost_price: 14000,
          status: 'active',
          track_inventory: false,
          inventory_quantity: 0,
          low_stock_threshold: 0,
          updated_at: '2024-01-12T11:30:00Z'
        }
      ]

      setInventory(sampleInventory)
    } catch (error) {
      console.error('Error loading inventory:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStockStatus = (item: InventoryItem) => {
    if (!item.track_inventory) {
      return { status: 'not_tracked', label: 'Not tracked', color: 'text-gray-500', icon: null }
    }
    
    if (item.inventory_quantity === 0) {
      return { 
        status: 'out_of_stock', 
        label: 'Out of stock', 
        color: 'text-red-600', 
        icon: XCircleIcon 
      }
    }
    
    if (item.inventory_quantity <= item.low_stock_threshold) {
      return { 
        status: 'low_stock', 
        label: 'Low stock', 
        color: 'text-yellow-600', 
        icon: ExclamationTriangleIcon 
      }
    }
    
    return { 
      status: 'in_stock', 
      label: 'In stock', 
      color: 'text-green-600', 
      icon: CheckCircleIcon 
    }
  }

  const filteredInventory = inventory
    .filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.sku?.toLowerCase().includes(searchQuery.toLowerCase())
      
      if (!matchesSearch) return false
      
      const stockStatus = getStockStatus(item)
      
      switch (stockFilter) {
        case 'in_stock':
          return stockStatus.status === 'in_stock'
        case 'low_stock':
          return stockStatus.status === 'low_stock'
        case 'out_of_stock':
          return stockStatus.status === 'out_of_stock'
        case 'not_tracked':
          return stockStatus.status === 'not_tracked'
        default:
          return true
      }
    })
    .sort((a, b) => {
      let aValue: any, bValue: any
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'quantity':
          aValue = a.inventory_quantity
          bValue = b.inventory_quantity
          break
        case 'updated':
          aValue = new Date(a.updated_at)
          bValue = new Date(b.updated_at)
          break
        default:
          return 0
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

  const handleQuantityUpdate = async (productId: string, newQty: number) => {
    try {
      // TODO: Implement quantity update API call
      console.log('Update quantity:', productId, newQty)
      
      // Update local state
      setInventory(prev => prev.map(item => 
        item.id === productId 
          ? { ...item, inventory_quantity: newQty }
          : item
      ))
      
      setEditingQuantity(null)
    } catch (error) {
      console.error('Error updating quantity:', error)
    }
  }

  const handleSort = (field: 'name' | 'quantity' | 'updated') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const getSortIcon = (field: 'name' | 'quantity' | 'updated') => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ArrowUpIcon : ArrowDownIcon
  }

  const stockStats = {
    total: inventory.length,
    inStock: inventory.filter(item => getStockStatus(item).status === 'in_stock').length,
    lowStock: inventory.filter(item => getStockStatus(item).status === 'low_stock').length,
    outOfStock: inventory.filter(item => getStockStatus(item).status === 'out_of_stock').length,
    notTracked: inventory.filter(item => getStockStatus(item).status === 'not_tracked').length
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Inventory Management</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Track and manage your product inventory levels
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">{stockStats.total}</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Products</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stockStats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">In Stock</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stockStats.inStock}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Low Stock</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stockStats.lowStock}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Out of Stock</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stockStats.outOfStock}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">NT</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Not Tracked</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stockStats.notTracked}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <select
                  value={stockFilter}
                  onChange={(e) => setStockFilter(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="all">All Stock Levels</option>
                  <option value="in_stock">In Stock</option>
                  <option value="low_stock">Low Stock</option>
                  <option value="out_of_stock">Out of Stock</option>
                  <option value="not_tracked">Not Tracked</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
        {filteredInventory.length === 0 ? (
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No inventory found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchQuery || stockFilter !== 'all'
                ? 'Try adjusting your search or filters.'
                : 'No products with inventory tracking found.'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    onClick={() => handleSort('name')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                  >
                    <div className="flex items-center space-x-1">
                      <span>Product</span>
                      {(() => {
                        const SortIcon = getSortIcon('name')
                        return SortIcon ? <SortIcon className="h-4 w-4" /> : null
                      })()}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th
                    onClick={() => handleSort('quantity')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                  >
                    <div className="flex items-center space-x-1">
                      <span>Quantity</span>
                      {(() => {
                        const SortIcon = getSortIcon('quantity')
                        return SortIcon ? <SortIcon className="h-4 w-4" /> : null
                      })()}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Low Stock Alert
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Value
                  </th>
                  <th
                    onClick={() => handleSort('updated')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                  >
                    <div className="flex items-center space-x-1">
                      <span>Last Updated</span>
                      {(() => {
                        const SortIcon = getSortIcon('updated')
                        return SortIcon ? <SortIcon className="h-4 w-4" /> : null
                      })()}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredInventory.map((item) => {
                  const stockStatus = getStockStatus(item)
                  const totalValue = item.inventory_quantity * (item.cost_price || item.price)
                  
                  return (
                    <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{item.sku}</div>
                          {item.category && (
                            <div className="text-xs text-gray-400 dark:text-gray-500">{item.category.name}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`flex items-center ${stockStatus.color}`}>
                          {stockStatus.icon && (
                            <stockStatus.icon className="h-4 w-4 mr-1" />
                          )}
                          <span className="text-sm">{stockStatus.label}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingQuantity === item.id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="number"
                              value={newQuantity}
                              onChange={(e) => setNewQuantity(parseInt(e.target.value) || 0)}
                              className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              min="0"
                              autoFocus
                            />
                            <button
                              onClick={() => handleQuantityUpdate(item.id, newQuantity)}
                              className="text-green-600 hover:text-green-800"
                            >
                              <CheckCircleIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => setEditingQuantity(null)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <XCircleIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-900 dark:text-white">
                              {item.track_inventory ? item.inventory_quantity : 'Not tracked'}
                            </span>
                            {item.track_inventory && (
                              <button
                                onClick={() => {
                                  setEditingQuantity(item.id)
                                  setNewQuantity(item.inventory_quantity)
                                }}
                                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                              >
                                <PencilIcon className="h-3 w-3" />
                              </button>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {item.track_inventory ? item.low_stock_threshold : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {item.track_inventory ? `PKR ${totalValue.toLocaleString()}` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(item.updated_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        <button className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300">
                          View Details
                        </button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
