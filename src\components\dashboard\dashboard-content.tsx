import React from 'react'
import { ArrowPathIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { WelcomeBanner } from './welcome-banner'
import { StatsCards } from './stats-cards'
import { RecentOrders } from './recent-orders'
import { SalesChart } from './sales-chart'
import { ActivityFeed } from './activity-feed'

import { AlertsPanel } from './alerts-panel'
import { useStore } from '@/contexts/store-context'
import { useRefreshAnalytics, useAnalyticsError } from '@/hooks/use-analytics'

export function DashboardContent() {
  const { currentStore } = useStore()
  const refreshAnalytics = useRefreshAnalytics()
  const analyticsError = useAnalyticsError()

  if (!currentStore) {
    return (
      <div className="space-y-6">
        <div className="admin-card p-8 text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-admin-warning mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-admin-text-primary mb-2">
            No Store Selected
          </h2>
          <p className="text-admin-text-secondary">
            Please select a store to view analytics data
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Banner */}
      <WelcomeBanner />

      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-serif font-bold text-admin-text-primary">
            Analytics Dashboard
          </h1>
          <p className="text-admin-text-secondary">
            Overview of {currentStore.name} performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          {analyticsError && (
            <div className="flex items-center text-admin-error text-sm">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              Data sync issue
            </div>
          )}
          <button
            onClick={refreshAnalytics}
            className="flex items-center gap-2 px-3 py-2 text-sm text-admin-text-secondary hover:text-admin-text-primary border border-admin-border rounded-md hover:bg-admin-hover transition-colors"
            title="Refresh all dashboard data"
          >
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <StatsCards />

      {/* Charts and Data */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SalesChart />
        <RecentOrders />
      </div>

      {/* Activity and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ActivityFeed />
        <AlertsPanel />
      </div>
    </div>
  )
}
