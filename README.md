# Womanza Admin Panel (Single Store Mode)

A dedicated Vite+React admin panel for managing the Womanza jewelry store with comprehensive role-based access control and modern features.

## Features

- 🔐 **Secure Authentication** - Supabase-powered auth with invitation-only access
- 👥 **Role-Based Access Control** - Super Admin, Admin, Editor, and Viewer roles
- 🎨 **Modern UI** - Clean, responsive design with dark/light themes
- ⚡ **Performance Optimized** - Vite build system with code splitting
- 🔄 **Real-time Updates** - Live data synchronization
- 📱 **Mobile Responsive** - Works on all device sizes
- 🧪 **Type Safe** - Full TypeScript support
- 📧 **User Invitation System** - Secure token-based user invitations
- 🛍️ **Complete Store Management** - Products, orders, customers, marketing, and more
- 🎯 **Single Store Focus** - Optimized for managing one dedicated store

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS
- **Routing**: TanStack Router
- **State Management**: Zustand, React Query
- **Backend**: Supabase
- **Forms**: React Hook Form + Zod
- **Charts**: Recharts
- **Icons**: Heroicons, Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd womanza-admin-panel
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_STORE_ID=womanza-store
VITE_STORE_NAME=Womanza Store
VITE_STORE_DOMAIN=womanza.com
```

5. Set up the database:
```bash
# Run the migration script in your Supabase SQL Editor
# Execute: database/single-store-migration.sql
```

6. Create your Super Admin user:
```sql
-- In Supabase SQL Editor, replace with your email
INSERT INTO public.store_users (user_id, store_id, role)
VALUES ('your-user-id', 'womanza-store', 'super_admin');
```

7. Start the development server:
```bash
npm run dev
```

The admin panel will be available at `http://localhost:5173`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication & invitation components
│   ├── customers/      # Customer management components
│   ├── dashboard/      # Dashboard and analytics components
│   ├── layout/         # Layout components (header, sidebar)
│   ├── marketing/      # Marketing and promotions components
│   ├── orders/         # Order management components
│   ├── products/       # Product management components
│   ├── settings/       # Settings and configuration components
│   ├── store/          # Store customization components
│   ├── ui/             # Base UI components
│   └── users/          # User management components
├── contexts/           # React contexts (auth, store, theme)
├── hooks/              # Custom hooks
├── lib/                # Utilities and configurations
├── routes/             # Route components
├── services/           # API services
└── types/              # TypeScript type definitions
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage

## User Roles & Permissions

| Role | Description | Permissions |
|------|-------------|-------------|
| **Super Admin** | Store owner with full control | All permissions, can manage other admins |
| **Admin** | Full panel access except Super Admin areas | Most permissions, can invite users |
| **Editor** | Content management access | Product, marketing, and content management |
| **Viewer** | Read-only access | View-only permissions across all modules |

## Core Modules

- **📊 Analytics Dashboard** - Sales, revenue, and performance metrics
- **🛍️ Product Management** - Products, categories, inventory tracking
- **📦 Order Management** - Order processing and fulfillment
- **👥 Customer Management** - Customer profiles and segmentation
- **📣 Marketing** - Promotions and email campaigns
- **👤 User Management** - Team member invitations and role management
- **🎨 Store Customization** - Branding and content management
- **⚙️ Settings** - Store configuration and integrations

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `VITE_SUPABASE_URL` | Supabase project URL | Yes | - |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes | - |
| `VITE_STORE_ID` | Store identifier | No | womanza-store |
| `VITE_STORE_NAME` | Store display name | No | Womanza Store |
| `VITE_STORE_DOMAIN` | Store domain | No | womanza.com |
| `VITE_ENABLE_ANALYTICS` | Enable analytics features | No | true |
| `VITE_ENABLE_USER_INVITES` | Enable user invitation system | No | true |
| `VITE_ENABLE_EMAIL_CAMPAIGNS` | Enable email marketing | No | true |
| `VITE_SESSION_TIMEOUT` | Session timeout in milliseconds | No | 3600000 |
| `VITE_INVITE_EXPIRY_DAYS` | Invitation expiry in days | No | 7 |

## Deployment

See [deployment.md](./deployment.md) for detailed deployment instructions.

### Quick Deploy to Vercel

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Configure custom domain: `admin.womanza.com`
4. Deploy automatically on push to main branch

### Quick Deploy to Netlify

1. Build the project: `npm run build`
2. Upload the `dist` folder to Netlify
3. Configure environment variables
4. Set up custom domain

## Database Setup

1. **Run Migration**: Execute `database/single-store-migration.sql` in Supabase SQL Editor
2. **Create Super Admin**: Insert your user into `store_users` table with `super_admin` role
3. **Verify Setup**: Test login and permissions

## Security Features

- **Invitation-Only Access** - No self-registration allowed
- **Role-Based Permissions** - Granular access control
- **Secure Tokens** - Time-limited invitation tokens
- **Row-Level Security** - Database-level access control
- **Session Management** - Automatic session timeout

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is proprietary and confidential.
