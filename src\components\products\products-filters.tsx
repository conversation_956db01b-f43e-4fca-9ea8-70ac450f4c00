import { useState, useEffect } from 'react'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import type { ProductFilter, Category } from '@/types/product'
import { METAL_TYPES, GEMSTONE_TYPES } from '@/types/product'

interface ProductsFiltersProps {
  filters: ProductFilter
  categories: Category[]
  onFilterChange: (filters: Partial<ProductFilter>) => void
  onReset: () => void
}

export function ProductsFilters({ 
  filters, 
  categories, 
  onFilterChange, 
  onReset 
}: ProductsFiltersProps) {
  const [localSearch, setLocalSearch] = useState(filters.search || '')

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearch !== filters.search) {
        onFilterChange({ search: localSearch || undefined })
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [localSearch, filters.search, onFilterChange])

  const handleClearSearch = () => {
    setLocalSearch('')
    onFilterChange({ search: undefined })
  }

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== undefined && value !== '' && value !== 1 && value !== 20 && 
    value !== 'created_at' && value !== 'desc'
  ).length

  return (
    <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-admin-text-primary">Filters</h3>
        {activeFiltersCount > 0 && (
          <button
            onClick={onReset}
            className="flex items-center gap-2 text-sm text-admin-text-secondary hover:text-admin-text-primary"
          >
            <XMarkIcon className="h-4 w-4" />
            Clear all ({activeFiltersCount})
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Search Products
          </label>
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-admin-text-secondary" />
            <input
              type="text"
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              placeholder="Search by name, SKU, or description..."
              className="w-full pl-10 pr-10 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            />
            {localSearch && (
              <button
                onClick={handleClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-admin-text-secondary hover:text-admin-text-primary"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Category
          </label>
          <select
            value={filters.category_id || ''}
            onChange={(e) => onFilterChange({ category_id: e.target.value || undefined })}
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Status
          </label>
          <select
            value={filters.status || ''}
            onChange={(e) => onFilterChange({ status: e.target.value as any || undefined })}
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>

        {/* Metal Type */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Metal Type
          </label>
          <select
            value={filters.metal_type || ''}
            onChange={(e) => onFilterChange({ metal_type: e.target.value || undefined })}
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          >
            <option value="">All Metals</option>
            {METAL_TYPES.map((metal) => (
              <option key={metal} value={metal}>
                {metal}
              </option>
            ))}
          </select>
        </div>

        {/* Gemstone Type */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Gemstone Type
          </label>
          <select
            value={filters.gemstone_type || ''}
            onChange={(e) => onFilterChange({ gemstone_type: e.target.value || undefined })}
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          >
            <option value="">All Gemstones</option>
            {GEMSTONE_TYPES.map((gemstone) => (
              <option key={gemstone} value={gemstone}>
                {gemstone}
              </option>
            ))}
          </select>
        </div>

        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Min Price
          </label>
          <input
            type="number"
            value={filters.price_min || ''}
            onChange={(e) => onFilterChange({ price_min: e.target.value ? Number(e.target.value) : undefined })}
            placeholder="0"
            min="0"
            step="0.01"
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-admin-text-primary mb-2">
            Max Price
          </label>
          <input
            type="number"
            value={filters.price_max || ''}
            onChange={(e) => onFilterChange({ price_max: e.target.value ? Number(e.target.value) : undefined })}
            placeholder="No limit"
            min="0"
            step="0.01"
            className="w-full py-2 px-3 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* Additional Filters */}
      <div className="mt-4 pt-4 border-t border-admin-border">
        <div className="flex flex-wrap gap-4">
          {/* Featured Products */}
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.is_featured || false}
              onChange={(e) => onFilterChange({ is_featured: e.target.checked || undefined })}
              className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
            />
            <span className="ml-2 text-sm text-admin-text-primary">Featured products only</span>
          </label>

          {/* Low Stock */}
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.low_stock || false}
              onChange={(e) => onFilterChange({ low_stock: e.target.checked || undefined })}
              className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
            />
            <span className="ml-2 text-sm text-admin-text-primary">Low stock items</span>
          </label>

          {/* Sort Options */}
          <div className="flex items-center gap-2">
            <label className="text-sm text-admin-text-primary">Sort by:</label>
            <select
              value={filters.sort_by || 'created_at'}
              onChange={(e) => onFilterChange({ sort_by: e.target.value as any })}
              className="py-1 px-2 border border-admin-border rounded bg-admin-bg-primary text-admin-text-primary text-sm focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              <option value="created_at">Date Created</option>
              <option value="updated_at">Date Updated</option>
              <option value="name">Name</option>
              <option value="price">Price</option>
              <option value="inventory_quantity">Stock</option>
            </select>

            <select
              value={filters.sort_order || 'desc'}
              onChange={(e) => onFilterChange({ sort_order: e.target.value as any })}
              className="py-1 px-2 border border-admin-border rounded bg-admin-bg-primary text-admin-text-primary text-sm focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              <option value="desc">Descending</option>
              <option value="asc">Ascending</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )
}
