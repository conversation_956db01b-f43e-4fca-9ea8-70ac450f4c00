-- 🔧 Fix User Data and Console Errors
-- Run this script in your Supabase SQL Editor

-- STEP 1: Check current state
SELECT 'Current auth users:' as info;
SELECT id, email, created_at FROM auth.users ORDER BY created_at;

SELECT 'Current store users:' as info;
SELECT su.id, su.user_id, su.role, au.email
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY su.created_at;

SELECT 'Current users table:' as info;
SELECT id, email, first_name, last_name, created_at FROM users ORDER BY created_at;

-- STEP 2: Fix user data issues
DO $$
DECLARE
    admin_user_id UUID;
    womanza_store_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Find the admin user by email in auth.users
    SELECT id INTO admin_user_id
    FROM auth.users
    WHERE email = '<EMAIL>'
    LIMIT 1;

    IF admin_user_id IS NOT NULL THEN
        -- Ensure user exists in users table
        INSERT INTO users (id, email, first_name, last_name, password_hash, created_at, updated_at)
        VALUES (
            admin_user_id,
            '<EMAIL>',
            'Usman',
            'Ali',
            'auth_user',
            NOW(),
            NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            updated_at = NOW();

        -- Ensure user exists in store_users table
        INSERT INTO store_users (user_id, store_id, role, created_at, updated_at)
        VALUES (
            admin_user_id,
            womanza_store_id,
            'admin',
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id, store_id) DO UPDATE SET
            role = EXCLUDED.role,
            updated_at = NOW();

        RAISE NOTICE 'Fixed user data for Usman Ali (admin role)';
    ELSE
        RAISE NOTICE 'Admin user not found with email: <EMAIL>';
    END IF;
END $$;

-- STEP 4: Check final state
SELECT 'Users after fix:' as info;
SELECT su.id, su.user_id, au.email, u.first_name, u.last_name, su.role, su.created_at 
FROM store_users su
LEFT JOIN auth.users au ON su.user_id = au.id
LEFT JOIN users u ON su.user_id = u.id
WHERE su.store_id = '550e8400-e29b-41d4-a716-************'
ORDER BY 
    CASE su.role 
        WHEN 'super_admin' THEN 1 
        WHEN 'admin' THEN 2 
        WHEN 'editor' THEN 3 
        WHEN 'viewer' THEN 4 
    END,
    su.created_at;

-- Success message
SELECT 'User details fixed successfully!' as status;
