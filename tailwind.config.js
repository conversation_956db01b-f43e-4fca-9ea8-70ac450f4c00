/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Admin theme colors
        admin: {
          primary: 'var(--admin-primary)',
          secondary: 'var(--admin-secondary)',
          accent: 'var(--admin-accent)',
          success: 'var(--admin-success)',
          warning: 'var(--admin-warning)',
          error: 'var(--admin-error)',
          info: 'var(--admin-info)',
          bg: {
            primary: 'var(--admin-bg-primary)',
            secondary: 'var(--admin-bg-secondary)',
            tertiary: 'var(--admin-bg-tertiary)',
          },
          text: {
            primary: 'var(--admin-text-primary)',
            secondary: 'var(--admin-text-secondary)',
            muted: 'var(--admin-text-muted)',
          },
          border: 'var(--admin-border)',
          hover: 'var(--admin-hover)',
        },
        // Brand colors
        orange: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
      },
      fontFamily: {
        serif: ['Georgia', 'Times New Roman', 'serif'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-slow': 'pulse 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
