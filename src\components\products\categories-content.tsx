import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { useStore } from '@/contexts/store-context'
import { ProductService } from '@/services/product-service'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  FolderIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'
import { Link } from '@tanstack/react-router'
import type { Category } from '@/types/product'

export function CategoriesContent() {
  const { currentStore } = useStore()
  const queryClient = useQueryClient()
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    is_active: true
  })

  // Fetch categories
  const { data: categories, isLoading } = useQuery({
    queryKey: ['categories', currentStore?.id],
    queryFn: () => ProductService.getCategories(currentStore!.id),
    enabled: !!currentStore?.id,
  })

  // Create category mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => ProductService.createCategory(currentStore!.id, data),
    onSuccess: () => {
      toast.success('Category created successfully!')
      queryClient.invalidateQueries({ queryKey: ['categories'] })
      resetForm()
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create category')
    },
  })

  // Update category mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      ProductService.updateCategory(id, data),
    onSuccess: () => {
      toast.success('Category updated successfully!')
      queryClient.invalidateQueries({ queryKey: ['categories'] })
      resetForm()
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update category')
    },
  })

  // Delete category mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => ProductService.deleteCategory(id),
    onSuccess: () => {
      toast.success('Category deleted successfully!')
      queryClient.invalidateQueries({ queryKey: ['categories'] })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete category')
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (editingCategory) {
      updateMutation.mutate({ id: editingCategory.id, data: formData })
    } else {
      createMutation.mutate(formData)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      is_active: category.is_active
    })
    setShowForm(true)
  }

  const handleDelete = (category: Category) => {
    if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
      deleteMutation.mutate(category.id)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      is_active: true
    })
    setEditingCategory(null)
    setShowForm(false)
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  if (!currentStore) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-admin-text-secondary">Please select a store to manage categories</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            to="/products"
            className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
            title="Back to products"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          
          <div>
            <h1 className="text-2xl font-semibold text-admin-text-primary">Categories</h1>
            <p className="mt-1 text-sm text-admin-text-secondary">
              Organize your products into categories
            </p>
          </div>
        </div>

        <button
          onClick={() => setShowForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-admin-primary text-white text-sm font-medium rounded-md hover:bg-admin-primary/90 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          Add Category
        </button>
      </div>

      {/* Category Form */}
      {showForm && (
        <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-6">
          <h3 className="text-lg font-medium text-admin-text-primary mb-4">
            {editingCategory ? 'Edit Category' : 'Add New Category'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-admin-text-primary mb-2">
                  Category Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => {
                    setFormData({ ...formData, name: e.target.value })
                    if (!editingCategory) {
                      setFormData(prev => ({ ...prev, slug: generateSlug(e.target.value) }))
                    }
                  }}
                  className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                  placeholder="Enter category name..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-admin-text-primary mb-2">
                  Slug *
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                  className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                  placeholder="category-slug"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-admin-text-primary mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary placeholder-admin-text-secondary focus:ring-2 focus:ring-admin-primary focus:border-transparent"
                placeholder="Category description..."
              />
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="rounded border-admin-border text-admin-primary focus:ring-admin-primary"
                />
                <span className="ml-2 text-sm text-admin-text-primary">Active</span>
              </label>
            </div>

            <div className="flex items-center gap-3">
              <button
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
                className="px-4 py-2 bg-admin-primary text-white text-sm font-medium rounded-md hover:bg-admin-primary/90 transition-colors disabled:opacity-50"
              >
                {createMutation.isPending || updateMutation.isPending 
                  ? 'Saving...' 
                  : editingCategory ? 'Update Category' : 'Create Category'
                }
              </button>
              
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 text-admin-text-secondary border border-admin-border rounded-md hover:text-admin-text-primary hover:bg-admin-hover transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Categories List */}
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <p className="text-admin-text-secondary">Loading categories...</p>
          </div>
        ) : !categories || categories.length === 0 ? (
          <div className="p-8 text-center">
            <FolderIcon className="mx-auto h-12 w-12 text-admin-text-secondary mb-4" />
            <h3 className="text-lg font-medium text-admin-text-primary mb-2">No categories yet</h3>
            <p className="text-admin-text-secondary mb-4">
              Create your first category to organize your products
            </p>
            <button
              onClick={() => setShowForm(true)}
              className="px-4 py-2 bg-admin-primary text-white text-sm font-medium rounded-md hover:bg-admin-primary/90 transition-colors"
            >
              Add Category
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-admin-border">
              <thead className="bg-admin-bg-primary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Products
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-admin-text-secondary uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-admin-bg-secondary divide-y divide-admin-border">
                {categories.map((category) => (
                  <tr key={category.id} className="hover:bg-admin-hover">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-admin-text-primary">
                          {category.name}
                        </div>
                        <div className="text-sm text-admin-text-secondary">
                          /{category.slug}
                        </div>
                        {category.description && (
                          <div className="text-xs text-admin-text-secondary mt-1">
                            {category.description}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-admin-text-primary">
                        {category.products_count || 0} products
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        category.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {category.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-admin-text-secondary">
                        {new Date(category.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => handleEdit(category)}
                          className="p-2 text-admin-text-secondary hover:text-admin-text-primary hover:bg-admin-hover rounded-md transition-colors"
                          title="Edit category"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDelete(category)}
                          className="p-2 text-admin-text-secondary hover:text-red-600 hover:bg-admin-hover rounded-md transition-colors"
                          title="Delete category"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
