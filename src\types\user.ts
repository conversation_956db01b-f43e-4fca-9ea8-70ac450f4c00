// User types for Womanza Admin Panel (Single Store Mode)

export type UserRole = 'super_admin' | 'admin' | 'editor' | 'viewer'

export interface UserPermissions {
  // Analytics
  view_analytics: boolean
  
  // Product Management
  manage_products: boolean
  view_products: boolean
  manage_categories: boolean
  manage_inventory: boolean
  
  // Order Management
  manage_orders: boolean
  view_orders: boolean
  update_order_status: boolean
  
  // Customer Management
  manage_customers: boolean
  view_customers: boolean
  manage_customer_groups: boolean
  
  // Marketing
  manage_marketing: boolean
  manage_promotions: boolean
  manage_email_campaigns: boolean
  
  // User Management (Super Admin and Admin only)
  manage_users: boolean
  invite_users: boolean
  manage_user_roles: boolean
  
  // Store Customization
  manage_store_settings: boolean
  manage_store_branding: boolean
  manage_store_content: boolean
  
  // System Settings
  manage_system_settings: boolean
  manage_payment_settings: boolean
  manage_shipping_settings: boolean
  manage_seo_settings: boolean
}

export interface WomanzaUser {
  id: string
  email: string
  role: UserRole
  permissions: UserPermissions
  is_active: boolean
  invited_by?: string
  invited_at?: string
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UserInvite {
  id: string
  email: string
  role: UserRole
  token: string
  invited_by: string
  expires_at: string
  used_at?: string
  created_at: string
}

// Role-based permission presets
export const ROLE_PERMISSIONS: Record<UserRole, UserPermissions> = {
  super_admin: {
    // Full access to everything
    view_analytics: true,
    manage_products: true,
    view_products: true,
    manage_categories: true,
    manage_inventory: true,
    manage_orders: true,
    view_orders: true,
    update_order_status: true,
    manage_customers: true,
    view_customers: true,
    manage_customer_groups: true,
    manage_marketing: true,
    manage_promotions: true,
    manage_email_campaigns: true,
    manage_users: true,
    invite_users: true,
    manage_user_roles: true,
    manage_store_settings: true,
    manage_store_branding: true,
    manage_store_content: true,
    manage_system_settings: true,
    manage_payment_settings: true,
    manage_shipping_settings: true,
    manage_seo_settings: true,
  },
  admin: {
    // Full panel control except Super Admin areas
    view_analytics: true,
    manage_products: true,
    view_products: true,
    manage_categories: true,
    manage_inventory: true,
    manage_orders: true,
    view_orders: true,
    update_order_status: true,
    manage_customers: true,
    view_customers: true,
    manage_customer_groups: true,
    manage_marketing: true,
    manage_promotions: true,
    manage_email_campaigns: true,
    manage_users: false, // Cannot manage other admins
    invite_users: true,
    manage_user_roles: false, // Cannot change roles of existing users
    manage_store_settings: true,
    manage_store_branding: true,
    manage_store_content: true,
    manage_system_settings: false, // Super Admin only
    manage_payment_settings: false, // Super Admin only
    manage_shipping_settings: true,
    manage_seo_settings: true,
  },
  editor: {
    // Limited access to content only
    view_analytics: true,
    manage_products: true,
    view_products: true,
    manage_categories: true,
    manage_inventory: false,
    manage_orders: false,
    view_orders: true,
    update_order_status: false,
    manage_customers: false,
    view_customers: true,
    manage_customer_groups: false,
    manage_marketing: true,
    manage_promotions: true,
    manage_email_campaigns: true,
    manage_users: false,
    invite_users: false,
    manage_user_roles: false,
    manage_store_settings: false,
    manage_store_branding: true,
    manage_store_content: true,
    manage_system_settings: false,
    manage_payment_settings: false,
    manage_shipping_settings: false,
    manage_seo_settings: true,
  },
  viewer: {
    // Read-only access
    view_analytics: true,
    manage_products: false,
    view_products: true,
    manage_categories: false,
    manage_inventory: false,
    manage_orders: false,
    view_orders: true,
    update_order_status: false,
    manage_customers: false,
    view_customers: true,
    manage_customer_groups: false,
    manage_marketing: false,
    manage_promotions: false,
    manage_email_campaigns: false,
    manage_users: false,
    invite_users: false,
    manage_user_roles: false,
    manage_store_settings: false,
    manage_store_branding: false,
    manage_store_content: false,
    manage_system_settings: false,
    manage_payment_settings: false,
    manage_shipping_settings: false,
    manage_seo_settings: false,
  },
}

// Helper functions
export function getUserPermissions(role: UserRole): UserPermissions {
  return ROLE_PERMISSIONS[role]
}

export function hasPermission(userRole: UserRole, permission: keyof UserPermissions): boolean {
  return ROLE_PERMISSIONS[userRole][permission]
}

export function canManageUser(currentUserRole: UserRole, targetUserRole: UserRole): boolean {
  // Super Admin can manage everyone
  if (currentUserRole === 'super_admin') return true
  
  // Admin can manage Editor and Viewer, but not other Admins or Super Admin
  if (currentUserRole === 'admin') {
    return targetUserRole === 'editor' || targetUserRole === 'viewer'
  }
  
  // Editor and Viewer cannot manage anyone
  return false
}

export function canInviteUser(currentUserRole: UserRole, targetRole: UserRole): boolean {
  // Super Admin can invite anyone
  if (currentUserRole === 'super_admin') return true
  
  // Admin can invite Editor and Viewer only
  if (currentUserRole === 'admin') {
    return targetRole === 'editor' || targetRole === 'viewer'
  }
  
  // Editor and Viewer cannot invite anyone
  return false
}

export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'super_admin':
      return 'Super Admin'
    case 'admin':
      return 'Admin'
    case 'editor':
      return 'Editor'
    case 'viewer':
      return 'Viewer'
    default:
      return 'Unknown'
  }
}

export function getRoleDescription(role: UserRole): string {
  switch (role) {
    case 'super_admin':
      return 'The real owner of Womanza Store. Full access and exclusive control'
    case 'admin':
      return 'Created/authorized by Super Admin. Full panel control, except Super Admin areas'
    case 'editor':
      return 'Created by Super Admin or Admin. Limited access to content only'
    case 'viewer':
      return 'Created by Super Admin or Admin. Read-only access'
    default:
      return 'Unknown role'
  }
}
