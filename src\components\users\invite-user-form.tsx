import React, { useState } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  ArrowLeftIcon,
  UserPlusIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { UserService } from '@/services/user-service'
import { UserRole, getRoleDisplayName, getRoleDescription, canInviteUser } from '@/types/user'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import toast from 'react-hot-toast'

const inviteSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['super_admin', 'admin', 'editor', 'viewer'] as const),
})

type InviteFormData = z.infer<typeof inviteSchema>

export function InviteUserForm() {
  const navigate = useNavigate()
  const { getUserRole } = useStore()
  const [loading, setLoading] = useState(false)
  
  const currentUserRole = getUserRole()
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      role: 'viewer',
    },
  })

  const selectedRole = watch('role')

  const availableRoles: UserRole[] = currentUserRole === 'super_admin' 
    ? ['admin', 'editor', 'viewer']
    : ['editor', 'viewer']

  const onSubmit = async (data: InviteFormData) => {
    if (!currentUserRole) {
      toast.error('Authentication required')
      return
    }

    if (!canInviteUser(currentUserRole, data.role)) {
      toast.error('You do not have permission to invite users with this role')
      return
    }

    setLoading(true)
    try {
      const response = await UserService.createInvite(data, currentUserRole)
      
      if (response.success) {
        toast.success('Invitation sent successfully!')
        navigate({ to: '/users' })
      } else {
        toast.error(response.error || 'Failed to send invitation')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate({ to: '/users' })}
          className="p-2 text-admin-text-muted hover:text-admin-text-primary hover:bg-admin-hover rounded-lg transition-colors"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-admin-text-primary">
            Invite New User
          </h1>
          <p className="text-admin-text-muted mt-1">
            Send an invitation to join the Womanza Admin Panel
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <div>
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...register('email')}
              error={errors.email?.message}
            />
            <p className="text-sm text-admin-text-muted mt-1">
              The user will receive an invitation email with setup instructions.
            </p>
          </div>

          {/* Role Selection */}
          <div>
            <Label htmlFor="role">Role</Label>
            <select
              id="role"
              {...register('role')}
              className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary focus:border-transparent"
            >
              {availableRoles.map((role) => (
                <option key={role} value={role}>
                  {getRoleDisplayName(role)}
                </option>
              ))}
            </select>
            {errors.role && (
              <p className="text-sm text-admin-error mt-1">{errors.role.message}</p>
            )}
          </div>

          {/* Role Description */}
          {selectedRole && (
            <div className="bg-admin-bg-primary rounded-lg p-4 border border-admin-border">
              <div className="flex items-start gap-3">
                <InformationCircleIcon className="h-5 w-5 text-admin-info flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-admin-text-primary mb-1">
                    {getRoleDisplayName(selectedRole)} Permissions
                  </h4>
                  <p className="text-sm text-admin-text-muted">
                    {getRoleDescription(selectedRole)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t border-admin-border">
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate({ to: '/users' })}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Sending...
                </>
              ) : (
                <>
                  <UserPlusIcon className="h-4 w-4" />
                  Send Invitation
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Help Text */}
      <div className="bg-admin-bg-primary rounded-lg p-4 border border-admin-border">
        <h3 className="font-medium text-admin-text-primary mb-2">
          What happens next?
        </h3>
        <ul className="text-sm text-admin-text-muted space-y-1">
          <li>• The user will receive an email invitation</li>
          <li>• They can click the link to set up their account</li>
          <li>• The invitation expires in 7 days</li>
          <li>• You can resend invitations if needed</li>
        </ul>
      </div>
    </div>
  )
}
