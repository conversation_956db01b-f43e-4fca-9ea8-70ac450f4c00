-- 🔧 Fix Foreign Key Issues - Run this FIRST
-- This script removes problematic foreign key constraints

-- Remove all foreign key constraints from store_users table
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS fk_store_users_user_id;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey1;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey2;

-- Make sure user_id column is just UUID without constraints
ALTER TABLE store_users ALTER COLUMN user_id TYPE UUID;

-- Check if there are any other foreign key constraints and remove them
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    FOR constraint_name IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'store_users'::regclass 
        AND contype = 'f'
    LOOP
        EXECUTE 'ALTER TABLE store_users DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
END $$;

-- Success message
SELECT 'Foreign key constraints removed successfully!' as status;
