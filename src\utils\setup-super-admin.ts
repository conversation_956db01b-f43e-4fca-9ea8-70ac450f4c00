// Utility to ensure super admin is properly set up in store_users
import { supabase } from '@/lib/supabase'
import { WOMANZA_STORE } from '@/lib/constants'

export async function ensureSuperAdminExists() {
  try {
    // Temporarily disable automatic super admin setup to prevent RLS recursion
    // Super admin should be set up via SQL scripts instead
    console.log('Super admin setup disabled - use SQL scripts for initial setup')
    return true

    // The following code is commented out to prevent RLS policy recursion
    /*
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.log('No authenticated user found')
      return false
    }

    // Check if user is the super admin email
    const superAdminEmails = ['<EMAIL>', '<EMAIL>']

    if (!superAdminEmails.includes(user.email || '')) {
      console.log('Current user is not a super admin')
      return false
    }

    // Note: Querying store_users here causes infinite recursion with RLS policies
    // This should be handled through SQL scripts instead
    */

  } catch (error) {
    console.error('Error in ensureSuperAdminExists:', error)
    return false
  }
}

export async function createSampleUsers() {
  try {
    // Disabled to prevent RLS policy recursion
    // Sample users should be created via invitation flow instead
    console.log('Sample user creation disabled - use invitation flow instead')
    return

  } catch (error) {
    console.error('Error creating sample users:', error)
  }
}

export async function setupInitialData() {
  try {
    // Ensure super admin exists
    await ensureSuperAdminExists()

    // Create sample users for testing
    await createSampleUsers()

    console.log('Initial data setup completed')
  } catch (error) {
    console.error('Error in setupInitialData:', error)
  }
}
