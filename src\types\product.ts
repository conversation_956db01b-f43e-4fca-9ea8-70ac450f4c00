export interface Product {
  id: string
  store_id: string
  category_id?: string
  name: string
  slug: string
  description?: string
  short_description?: string
  sku?: string
  barcode?: string
  
  // Pricing
  price: number
  compare_at_price?: number
  cost_price?: number
  
  // Inventory
  track_inventory: boolean
  inventory_quantity: number
  low_stock_threshold: number
  allow_backorder: boolean
  
  // Jewelry specific attributes
  metal_type?: string
  gemstone_type?: string
  gemstone_carat?: number
  metal_purity?: string
  weight_grams?: number
  dimensions?: Record<string, any>
  
  // Media
  images: string[]
  videos: string[]
  
  // SEO
  meta_title?: string
  meta_description?: string
  
  // Status and visibility
  status: 'draft' | 'active' | 'archived'
  is_featured: boolean
  requires_shipping: boolean
  is_digital: boolean
  
  // Timestamps
  published_at?: string
  created_at: string
  updated_at: string
  
  // Relations
  category?: Category
}

export interface Category {
  id: string
  store_id?: string
  name: string
  slug: string
  description?: string
  image?: string
  parent_id?: string
  sort_order?: number
  is_active: boolean
  created_at: string
  updated_at: string
  
  // Relations
  parent?: Category
  children?: Category[]
  products_count?: number
}

export interface ProductVariant {
  id: string
  product_id: string
  title: string
  sku?: string
  barcode?: string
  price?: number
  compare_at_price?: number
  cost_price?: number
  inventory_quantity: number
  weight?: number
  requires_shipping: boolean
  taxable: boolean
  position: number
  created_at: string
  updated_at: string
}

export interface ProductImage {
  id: string
  product_id: string
  src: string
  alt?: string
  position: number
  width?: number
  height?: number
  created_at: string
}

export interface ProductFilter {
  search?: string
  category_id?: string
  status?: Product['status']
  is_featured?: boolean
  low_stock?: boolean
  price_min?: number
  price_max?: number
  metal_type?: string
  gemstone_type?: string
  sort_by?: 'name' | 'price' | 'created_at' | 'updated_at' | 'inventory_quantity'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface ProductFormData {
  name: string
  slug: string
  description?: string
  short_description?: string
  sku?: string
  barcode?: string
  category_id?: string
  
  // Pricing
  price: number
  compare_at_price?: number
  cost_price?: number
  
  // Inventory
  track_inventory: boolean
  inventory_quantity: number
  low_stock_threshold: number
  allow_backorder: boolean
  
  // Jewelry specific
  metal_type?: string
  gemstone_type?: string
  gemstone_carat?: number
  metal_purity?: string
  weight_grams?: number
  dimensions?: Record<string, any>
  
  // Media
  images: string[]
  videos: string[]
  
  // SEO
  meta_title?: string
  meta_description?: string
  
  // Status
  status: Product['status']
  is_featured: boolean
  requires_shipping: boolean
  is_digital: boolean
}

export interface BulkProductAction {
  action: 'delete' | 'update_status' | 'update_category' | 'update_featured'
  product_ids: string[]
  data?: {
    status?: Product['status']
    category_id?: string
    is_featured?: boolean
  }
}

export interface ProductStats {
  total_products: number
  active_products: number
  draft_products: number
  low_stock_products: number
  out_of_stock_products: number
  featured_products: number
  total_value: number
  average_price: number
}

// Jewelry-specific enums
export const METAL_TYPES = [
  'Gold',
  'Silver',
  'Platinum',
  'Rose Gold',
  'White Gold',
  'Yellow Gold',
  'Stainless Steel',
  'Titanium',
  'Copper',
  'Brass'
] as const

export const GEMSTONE_TYPES = [
  'Diamond',
  'Ruby',
  'Sapphire',
  'Emerald',
  'Pearl',
  'Amethyst',
  'Topaz',
  'Garnet',
  'Opal',
  'Turquoise',
  'Onyx',
  'Jade',
  'Aquamarine',
  'Citrine',
  'Peridot'
] as const

export const METAL_PURITIES = [
  '24K',
  '22K',
  '18K',
  '14K',
  '10K',
  '925 Sterling',
  '950 Platinum',
  '999 Fine Silver'
] as const

export type MetalType = typeof METAL_TYPES[number]
export type GemstoneType = typeof GEMSTONE_TYPES[number]
export type MetalPurity = typeof METAL_PURITIES[number]
