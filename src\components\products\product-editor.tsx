import { useState, useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { useStore } from '@/contexts/store-context'
import { ProductService } from '@/services/product-service'
import { ProductEditorTabs } from './product-editor-tabs'
import { ProductEditorHeader } from './product-editor-header'
import { ProductBasicInfo } from './product-basic-info'
import { ProductPricing } from './product-pricing'
import { ProductInventory } from './product-inventory'
import { ProductJewelryDetails } from './product-jewelry-details'
import { ProductImages } from './product-images'
import { ProductSEO } from './product-seo'
import type { ProductFormData, Product } from '@/types/product'

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().optional(),
  short_description: z.string().optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  category_id: z.string().optional(),
  
  // Pricing
  price: z.number().min(0, 'Price must be positive'),
  compare_at_price: z.number().optional(),
  cost_price: z.number().optional(),
  
  // Inventory
  track_inventory: z.boolean().default(true),
  inventory_quantity: z.number().min(0, 'Inventory must be positive').default(0),
  low_stock_threshold: z.number().min(0, 'Threshold must be positive').default(5),
  allow_backorder: z.boolean().default(false),
  
  // Jewelry specific
  metal_type: z.string().optional(),
  gemstone_type: z.string().optional(),
  gemstone_carat: z.number().optional(),
  metal_purity: z.string().optional(),
  weight_grams: z.number().optional(),
  dimensions: z.record(z.any()).optional(),
  
  // Media
  images: z.array(z.string()).default([]),
  videos: z.array(z.string()).default([]),
  
  // SEO
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),
  
  // Status
  status: z.enum(['draft', 'active', 'archived']).default('draft'),
  is_featured: z.boolean().default(false),
  requires_shipping: z.boolean().default(true),
  is_digital: z.boolean().default(false),
})

interface ProductEditorProps {
  mode: 'create' | 'edit'
  productId?: string
}

export function ProductEditor({ mode, productId }: ProductEditorProps) {
  const navigate = useNavigate()
  const { currentStore } = useStore()
  const [activeTab, setActiveTab] = useState('basic')
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // Fetch existing product for edit mode
  const { data: existingProduct, isLoading: productLoading } = useQuery({
    queryKey: ['product', productId],
    queryFn: () => ProductService.getProduct(productId!),
    enabled: mode === 'edit' && !!productId,
  })

  // Fetch categories for dropdown
  const { data: categories } = useQuery({
    queryKey: ['categories', currentStore?.id],
    queryFn: () => ProductService.getCategories(currentStore!.id),
    enabled: !!currentStore?.id,
  })

  // Form setup
  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      short_description: '',
      sku: '',
      barcode: '',
      category_id: '',
      price: 0,
      compare_at_price: undefined,
      cost_price: undefined,
      track_inventory: true,
      inventory_quantity: 0,
      low_stock_threshold: 5,
      allow_backorder: false,
      metal_type: '',
      gemstone_type: '',
      gemstone_carat: undefined,
      metal_purity: '',
      weight_grams: undefined,
      dimensions: {},
      images: [],
      videos: [],
      meta_title: '',
      meta_description: '',
      status: 'draft',
      is_featured: false,
      requires_shipping: true,
      is_digital: false,
    },
  })

  // Populate form with existing product data
  useEffect(() => {
    if (existingProduct && mode === 'edit') {
      form.reset({
        name: existingProduct.name,
        slug: existingProduct.slug,
        description: existingProduct.description || '',
        short_description: existingProduct.short_description || '',
        sku: existingProduct.sku || '',
        barcode: existingProduct.barcode || '',
        category_id: existingProduct.category_id || '',
        price: existingProduct.price,
        compare_at_price: existingProduct.compare_at_price,
        cost_price: existingProduct.cost_price,
        track_inventory: existingProduct.track_inventory,
        inventory_quantity: existingProduct.inventory_quantity,
        low_stock_threshold: existingProduct.low_stock_threshold,
        allow_backorder: existingProduct.allow_backorder,
        metal_type: existingProduct.metal_type || '',
        gemstone_type: existingProduct.gemstone_type || '',
        gemstone_carat: existingProduct.gemstone_carat,
        metal_purity: existingProduct.metal_purity || '',
        weight_grams: existingProduct.weight_grams,
        dimensions: existingProduct.dimensions || {},
        images: existingProduct.images || [],
        videos: existingProduct.videos || [],
        meta_title: existingProduct.meta_title || '',
        meta_description: existingProduct.meta_description || '',
        status: existingProduct.status,
        is_featured: existingProduct.is_featured,
        requires_shipping: existingProduct.requires_shipping,
        is_digital: existingProduct.is_digital,
      })
    }
  }, [existingProduct, mode, form])

  // Auto-generate slug from name
  const watchedName = form.watch('name')
  useEffect(() => {
    if (watchedName && mode === 'create') {
      const slug = ProductService.generateSlug(watchedName)
      form.setValue('slug', slug)
    }
  }, [watchedName, mode, form])

  // Create product mutation
  const createMutation = useMutation({
    mutationFn: (data: ProductFormData) => 
      ProductService.createProduct(currentStore!.id, data),
    onSuccess: (product) => {
      toast.success('Product created successfully!')
      navigate({ to: `/products/${product.id}` })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create product')
    },
  })

  // Update product mutation
  const updateMutation = useMutation({
    mutationFn: (data: ProductFormData) => 
      ProductService.updateProduct(productId!, data),
    onSuccess: (product) => {
      toast.success('Product updated successfully!')
      navigate({ to: `/products/${product.id}` })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update product')
    },
  })

  const onSubmit = (data: ProductFormData) => {
    if (mode === 'create') {
      createMutation.mutate(data)
    } else {
      updateMutation.mutate(data)
    }
  }

  const handleSaveDraft = () => {
    form.setValue('status', 'draft')
    form.handleSubmit(onSubmit)()
  }

  const handlePublish = () => {
    form.setValue('status', 'active')
    form.handleSubmit(onSubmit)()
  }

  const handlePreview = () => {
    setIsPreviewMode(!isPreviewMode)
  }

  const isLoading = createMutation.isPending || updateMutation.isPending || productLoading

  if (!currentStore) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-admin-text-secondary">Please select a store to manage products</p>
        </div>
      </div>
    )
  }

  if (productLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-admin-text-secondary">Loading product...</p>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'basic', name: 'Basic Information', icon: '📝' },
    { id: 'pricing', name: 'Pricing', icon: '💰' },
    { id: 'inventory', name: 'Inventory', icon: '📦' },
    { id: 'jewelry', name: 'Jewelry Details', icon: '💎' },
    { id: 'images', name: 'Images & Media', icon: '🖼️' },
    { id: 'seo', name: 'SEO & Metadata', icon: '🔍' },
  ]

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <ProductEditorHeader
        mode={mode}
        productName={form.watch('name') || 'New Product'}
        isLoading={isLoading}
        onSaveDraft={handleSaveDraft}
        onPublish={handlePublish}
        onPreview={handlePreview}
        isPreviewMode={isPreviewMode}
      />

      {/* Tabs */}
      <ProductEditorTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Tab Content */}
      <div className="bg-admin-bg-secondary border border-admin-border rounded-lg p-6">
        {activeTab === 'basic' && (
          <ProductBasicInfo
            form={form}
            categories={categories || []}
          />
        )}

        {activeTab === 'pricing' && (
          <ProductPricing form={form} />
        )}

        {activeTab === 'inventory' && (
          <ProductInventory form={form} />
        )}

        {activeTab === 'jewelry' && (
          <ProductJewelryDetails form={form} />
        )}

        {activeTab === 'images' && (
          <ProductImages form={form} />
        )}

        {activeTab === 'seo' && (
          <ProductSEO form={form} />
        )}
      </div>
    </form>
  )
}
