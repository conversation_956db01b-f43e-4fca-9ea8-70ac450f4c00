-- 🔧 Fix Admin Access Issues
-- Run this script in your Supabase SQL Editor

-- STEP 1: Drop current restrictive policies
DROP POLICY IF EXISTS "temp_allow_all_store_users" ON store_users;
DROP POLICY IF EXISTS "temp_allow_all_users" ON users;

-- STEP 2: Create proper role-based policies for store_users
-- Allow users to view their own store record
CREATE POLICY "view_own_store_record" ON store_users
    FOR SELECT USING (user_id = auth.uid());

-- Allow super_admin and admin to view all store users
CREATE POLICY "admins_view_all_store_users" ON store_users
    FOR SELECT USING (
        -- Check if current user has admin privileges in this store
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.store_id = store_users.store_id 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Allow super_admin and admin to insert new store users
CREATE POLICY "admins_insert_store_users" ON store_users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.store_id = store_users.store_id 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Allow super_admin and admin to update store users
CREATE POLICY "admins_update_store_users" ON store_users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.store_id = store_users.store_id 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Allow super_admin and admin to delete store users (except themselves)
CREATE POLICY "admins_delete_store_users" ON store_users
    FOR DELETE USING (
        user_id != auth.uid() AND
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.store_id = store_users.store_id 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- STEP 3: Create proper policies for users table
-- Allow authenticated users to view users (needed for user management)
CREATE POLICY "authenticated_view_users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow super_admin and admin to insert users
CREATE POLICY "admins_insert_users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Allow super_admin and admin to update users
CREATE POLICY "admins_update_users" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Allow super_admin and admin to delete users
CREATE POLICY "admins_delete_users" ON users
    FOR DELETE USING (
        id != auth.uid() AND
        EXISTS (
            SELECT 1 FROM store_users su 
            WHERE su.user_id = auth.uid() 
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- STEP 4: Ensure the current admin user exists in store_users
-- Insert admin user if not exists (replace with actual admin user ID)
INSERT INTO store_users (id, user_id, store_id, role, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    auth.uid(),
    '550e8400-e29b-41d4-a716-************',
    'admin',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM store_users 
    WHERE user_id = auth.uid() 
    AND store_id = '550e8400-e29b-41d4-a716-************'
);

-- Success message
SELECT 'Admin access policies fixed! Admin should now have proper permissions.' as status;
