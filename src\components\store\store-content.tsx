import React, { useState } from 'react'
import {
  PaintBrushIcon,
  PhotoIcon,
  CogIcon,
  EyeIcon,
  SwatchIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import toast from 'react-hot-toast'

export function StoreContent() {
  const { hasPermission, currentStore } = useStore()
  const [activeTab, setActiveTab] = useState<'branding' | 'content'>('branding')
  const [loading, setLoading] = useState(false)

  // Store branding state
  const [branding, setBranding] = useState({
    logo: currentStore.settings?.logo || '',
    primary_color: currentStore.settings?.primary_color || '#D946EF',
    secondary_color: currentStore.settings?.secondary_color || '#EC4899',
    accent_color: currentStore.settings?.accent_color || '#F59E0B',
    font_family: currentStore.settings?.font_family || 'Inter',
  })

  // Store content state
  const [content, setContent] = useState({
    hero_title: 'Discover Timeless Elegance',
    hero_subtitle: 'Premium jewelry and accessories for the modern woman',
    about_text: 'At Womanza, we believe every woman deserves to feel beautiful and confident. Our carefully curated collection features stunning jewelry pieces that celebrate your unique style.',
    footer_text: '© 2024 Womanza Store. All rights reserved.',
  })

  const handleBrandingSave = async () => {
    if (!hasPermission('manage_store_branding')) {
      toast.error('You do not have permission to update store branding')
      return
    }

    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Branding updated successfully!')
    } catch (error) {
      toast.error('Failed to update branding')
    } finally {
      setLoading(false)
    }
  }

  const handleContentSave = async () => {
    if (!hasPermission('manage_store_content')) {
      toast.error('You do not have permission to update store content')
      return
    }

    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Content updated successfully!')
    } catch (error) {
      toast.error('Failed to update content')
    } finally {
      setLoading(false)
    }
  }

  if (!hasPermission('manage_store_settings')) {
    return (
      <div className="text-center py-12">
        <CogIcon className="h-12 w-12 text-admin-error mx-auto mb-4" />
        <h3 className="text-lg font-medium text-admin-text-primary mb-2">
          Access Denied
        </h3>
        <p className="text-admin-text-muted">
          You don't have permission to manage store settings.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-admin-text-primary">
            Store Customization
          </h1>
          <p className="text-admin-text-muted mt-1">
            Customize your store's appearance and content
          </p>
        </div>
        <Button variant="ghost" className="flex items-center gap-2">
          <EyeIcon className="h-4 w-4" />
          Preview Store
        </Button>
      </div>

      {/* Tabs */}
      <div className="border-b border-admin-border">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('branding')}
            className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
              activeTab === 'branding'
                ? 'border-admin-primary text-admin-primary'
                : 'border-transparent text-admin-text-muted hover:text-admin-text-secondary hover:border-admin-border'
            }`}
          >
            <PaintBrushIcon className="h-4 w-4" />
            Branding
          </button>
          <button
            onClick={() => setActiveTab('content')}
            className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
              activeTab === 'content'
                ? 'border-admin-primary text-admin-primary'
                : 'border-transparent text-admin-text-muted hover:text-admin-text-secondary hover:border-admin-border'
            }`}
          >
            <DocumentTextIcon className="h-4 w-4" />
            Content
          </button>
        </nav>
      </div>

      {/* Branding Tab */}
      {activeTab === 'branding' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Settings Form */}
          <div className="space-y-6">
            <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
              <h3 className="text-lg font-medium text-admin-text-primary mb-4 flex items-center gap-2">
                <SwatchIcon className="h-5 w-5" />
                Brand Colors
              </h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="primary_color">Primary Color</Label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      id="primary_color"
                      value={branding.primary_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, primary_color: e.target.value }))}
                      className="w-12 h-10 rounded border border-admin-border"
                    />
                    <Input
                      value={branding.primary_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, primary_color: e.target.value }))}
                      placeholder="#D946EF"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="secondary_color">Secondary Color</Label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      id="secondary_color"
                      value={branding.secondary_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, secondary_color: e.target.value }))}
                      className="w-12 h-10 rounded border border-admin-border"
                    />
                    <Input
                      value={branding.secondary_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, secondary_color: e.target.value }))}
                      placeholder="#EC4899"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="accent_color">Accent Color</Label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      id="accent_color"
                      value={branding.accent_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, accent_color: e.target.value }))}
                      className="w-12 h-10 rounded border border-admin-border"
                    />
                    <Input
                      value={branding.accent_color}
                      onChange={(e) => setBranding(prev => ({ ...prev, accent_color: e.target.value }))}
                      placeholder="#F59E0B"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
              <h3 className="text-lg font-medium text-admin-text-primary mb-4 flex items-center gap-2">
                <PhotoIcon className="h-5 w-5" />
                Logo & Typography
              </h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="logo">Logo URL</Label>
                  <Input
                    id="logo"
                    value={branding.logo}
                    onChange={(e) => setBranding(prev => ({ ...prev, logo: e.target.value }))}
                    placeholder="https://example.com/logo.png"
                  />
                  <p className="text-sm text-admin-text-muted mt-1">
                    Upload your logo and paste the URL here
                  </p>
                </div>
                <div>
                  <Label htmlFor="font_family">Font Family</Label>
                  <select
                    id="font_family"
                    value={branding.font_family}
                    onChange={(e) => setBranding(prev => ({ ...prev, font_family: e.target.value }))}
                    className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                  >
                    <option value="Inter">Inter</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Playfair Display">Playfair Display</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={handleBrandingSave}
                disabled={loading || !hasPermission('manage_store_branding')}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Branding'
                )}
              </Button>
            </div>
          </div>

          {/* Preview */}
          <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
            <h3 className="text-lg font-medium text-admin-text-primary mb-4">
              Preview
            </h3>
            <div className="bg-white rounded-lg p-6 border" style={{ fontFamily: branding.font_family }}>
              <div className="flex items-center gap-3 mb-6">
                {branding.logo ? (
                  <img src={branding.logo} alt="Logo" className="h-8 w-8 rounded" />
                ) : (
                  <div 
                    className="h-8 w-8 rounded flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: branding.primary_color }}
                  >
                    W
                  </div>
                )}
                <span className="text-xl font-bold" style={{ color: branding.primary_color }}>
                  {currentStore.name}
                </span>
              </div>
              <div className="space-y-4">
                <button 
                  className="px-4 py-2 rounded text-white font-medium"
                  style={{ backgroundColor: branding.primary_color }}
                >
                  Shop Now
                </button>
                <button 
                  className="px-4 py-2 rounded text-white font-medium ml-2"
                  style={{ backgroundColor: branding.secondary_color }}
                >
                  Learn More
                </button>
                <div className="mt-4">
                  <span 
                    className="inline-block px-2 py-1 rounded text-xs font-medium text-white"
                    style={{ backgroundColor: branding.accent_color }}
                  >
                    Sale
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Content Tab */}
      {activeTab === 'content' && (
        <div className="max-w-4xl">
          <div className="bg-admin-bg-secondary rounded-lg border border-admin-border p-6">
            <div className="space-y-6">
              <div>
                <Label htmlFor="hero_title">Hero Section Title</Label>
                <Input
                  id="hero_title"
                  value={content.hero_title}
                  onChange={(e) => setContent(prev => ({ ...prev, hero_title: e.target.value }))}
                  placeholder="Discover Timeless Elegance"
                />
              </div>
              <div>
                <Label htmlFor="hero_subtitle">Hero Section Subtitle</Label>
                <Input
                  id="hero_subtitle"
                  value={content.hero_subtitle}
                  onChange={(e) => setContent(prev => ({ ...prev, hero_subtitle: e.target.value }))}
                  placeholder="Premium jewelry and accessories for the modern woman"
                />
              </div>
              <div>
                <Label htmlFor="about_text">About Section</Label>
                <textarea
                  id="about_text"
                  value={content.about_text}
                  onChange={(e) => setContent(prev => ({ ...prev, about_text: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-admin-border rounded-md bg-admin-bg-primary text-admin-text-primary focus:outline-none focus:ring-2 focus:ring-admin-primary"
                  placeholder="Tell your brand story..."
                />
              </div>
              <div>
                <Label htmlFor="footer_text">Footer Text</Label>
                <Input
                  id="footer_text"
                  value={content.footer_text}
                  onChange={(e) => setContent(prev => ({ ...prev, footer_text: e.target.value }))}
                  placeholder="© 2024 Womanza Store. All rights reserved."
                />
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <Button
                onClick={handleContentSave}
                disabled={loading || !hasPermission('manage_store_content')}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Content'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
