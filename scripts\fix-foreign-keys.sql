-- 🔧 Fix Foreign Key Issues - Run this FIRST
-- This script removes problematic foreign key constraints

-- STEP 1: Drop all policies that depend on user_id column
DROP POLICY IF EXISTS "user_view_own" ON store_users;
DROP POLICY IF EXISTS "Users can view own store users" ON store_users;
DROP POLICY IF EXISTS "Store users can view store users" ON store_users;
DROP POLICY IF EXISTS "Store admins can manage store users" ON store_users;

-- STEP 2: Remove all foreign key constraints from store_users table
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS fk_store_users_user_id;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey1;
ALTER TABLE store_users DROP CONSTRAINT IF EXISTS store_users_user_id_fkey2;

-- STEP 3: Make sure user_id column is just UUID without constraints
ALTER TABLE store_users ALTER COLUMN user_id TYPE UUID;

-- Check if there are any other foreign key constraints and remove them
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    FOR constraint_name IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'store_users'::regclass 
        AND contype = 'f'
    LOOP
        EXECUTE 'ALTER TABLE store_users DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
END $$;

-- STEP 4: Recreate essential policies for store_users
CREATE POLICY "Users can view own store users" ON store_users
    FOR SELECT USING (
        user_id = auth.uid() OR
        store_id IN (
            SELECT store_id
            FROM store_users
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Store admins can manage store users" ON store_users
    FOR ALL USING (
        EXISTS (
            SELECT 1
            FROM store_users su
            WHERE su.user_id = auth.uid()
            AND su.store_id = store_users.store_id
            AND su.role IN ('super_admin', 'admin')
        )
    );

-- Success message
SELECT 'Foreign key constraints and policies fixed successfully!' as status;
