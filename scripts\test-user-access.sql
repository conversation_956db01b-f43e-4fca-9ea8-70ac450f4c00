-- 🧪 Test User Access and Permissions
-- Run this to verify everything is working

-- STEP 1: Check if admin_users table exists and has data
SELECT 'Checking admin_users table...' as test;

SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as super_admins,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
    COUNT(CASE WHEN role = 'editor' THEN 1 END) as editors,
    COUNT(CASE WHEN role = 'viewer' THEN 1 END) as viewers
FROM admin_users;

-- STEP 2: Check current user access
SELECT 'Checking current user access...' as test;

SELECT 
    id,
    email,
    full_name,
    role,
    store_id,
    created_at
FROM admin_users 
WHERE id = 'a9c28287-fff6-4ac4-b4a8-5e0b8a4f9315'
   OR email = '<EMAIL>'
   OR email = '<EMAIL>';

-- STEP 3: Test RLS policies
SELECT 'Testing RLS policies...' as test;

-- This should work if user has proper access
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ RLS policies working - user can access data'
        ELSE '❌ RLS policies blocking access'
    END as rls_status
FROM admin_users 
WHERE store_id = '550e8400-e29b-41d4-a716-446655440001';

-- STEP 4: Check auth.users table
SELECT 'Checking auth.users...' as test;

SELECT 
    id,
    email,
    created_at,
    email_confirmed_at
FROM auth.users 
ORDER BY created_at;

-- STEP 5: Final status
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM admin_users 
            WHERE id = 'a9c28287-fff6-4ac4-b4a8-5e0b8a4f9315'
        ) THEN '✅ SUCCESS: Current user has admin access!'
        ELSE '❌ ISSUE: Current user not found in admin_users'
    END as final_status;

SELECT '🎉 User access test complete!' as result;
