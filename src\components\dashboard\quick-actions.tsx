import React from 'react'
import {
  PlusIcon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  TagIcon,
  MegaphoneIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
} from '@heroicons/react/24/outline'
import { useStore } from '@/contexts/store-context'

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  action: () => void
  permission?: string
}

export function QuickActions() {
  const { hasPermission } = useStore()

  const actions: QuickAction[] = [
    {
      id: 'add-product',
      title: 'Add Product',
      description: 'Create a new product listing',
      icon: PlusIcon,
      color: 'bg-admin-primary hover:bg-admin-primary/90',
      action: () => console.log('Navigate to add product'),
      permission: 'manage_products',
    },
    {
      id: 'create-order',
      title: 'Create Order',
      description: 'Process a new customer order',
      icon: ClipboardDocumentListIcon,
      color: 'bg-admin-success hover:bg-admin-success/90',
      action: () => console.log('Navigate to create order'),
      permission: 'manage_orders',
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      description: 'Register a new customer',
      icon: UsersIcon,
      color: 'bg-admin-info hover:bg-admin-info/90',
      action: () => console.log('Navigate to add customer'),
      permission: 'manage_customers',
    },
    {
      id: 'create-promotion',
      title: 'Create Promotion',
      description: 'Set up a new discount or offer',
      icon: TagIcon,
      color: 'bg-admin-warning hover:bg-admin-warning/90',
      action: () => console.log('Navigate to create promotion'),
      permission: 'manage_marketing',
    },
    {
      id: 'send-campaign',
      title: 'Email Campaign',
      description: 'Send marketing email to customers',
      icon: MegaphoneIcon,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => console.log('Navigate to email campaign'),
      permission: 'manage_marketing',
    },
    {
      id: 'view-reports',
      title: 'View Reports',
      description: 'Access detailed analytics',
      icon: ChartBarIcon,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      action: () => console.log('Navigate to reports'),
      permission: 'view_analytics',
    },
    {
      id: 'manage-inventory',
      title: 'Manage Inventory',
      description: 'Update stock levels',
      icon: ShoppingBagIcon,
      color: 'bg-emerald-500 hover:bg-emerald-600',
      action: () => console.log('Navigate to inventory'),
      permission: 'manage_products',
    },
    {
      id: 'store-settings',
      title: 'Store Settings',
      description: 'Configure store preferences',
      icon: CogIcon,
      color: 'bg-gray-500 hover:bg-gray-600',
      action: () => console.log('Navigate to settings'),
      permission: 'manage_settings',
    },
  ]

  const filteredActions = actions.filter(action => 
    !action.permission || hasPermission(action.permission)
  )

  return (
    <div className="admin-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-serif font-semibold text-admin-text-primary">
          Quick Actions
        </h3>
        <button className="text-sm text-admin-primary hover:text-admin-primary/80">
          Customize
        </button>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {filteredActions.slice(0, 8).map((action) => (
          <button
            key={action.id}
            onClick={action.action}
            className={`${action.color} text-white p-4 rounded-lg transition-all hover:scale-105 hover:shadow-md group`}
          >
            <div className="flex flex-col items-center text-center">
              <action.icon className="h-6 w-6 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium">{action.title}</span>
              <span className="text-xs opacity-90 mt-1 hidden md:block">
                {action.description}
              </span>
            </div>
          </button>
        ))}
      </div>

      {filteredActions.length === 0 && (
        <div className="text-center py-8">
          <DocumentTextIcon className="h-8 w-8 text-admin-text-muted mx-auto mb-2" />
          <p className="text-sm text-admin-text-muted">No actions available</p>
          <p className="text-xs text-admin-text-muted mt-1">
            Actions will appear based on your permissions
          </p>
        </div>
      )}
    </div>
  )
}
