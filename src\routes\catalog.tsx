import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { CatalogContent } from '@/components/catalog/catalog-content'

export const Route = createFileRoute('/catalog')({
  component: Catalog,
})

function Catalog() {
  const location = useLocation()

  // If we're on a sub-route, render the outlet instead of the main catalog content
  if (location.pathname !== '/catalog') {
    return (
      <DashboardLayout>
        <Outlet />
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <CatalogContent />
    </DashboardLayout>
  )
}
