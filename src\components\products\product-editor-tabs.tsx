interface Tab {
  id: string
  name: string
  icon: string
}

interface ProductEditorTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
}

export function ProductEditorTabs({ tabs, activeTab, onTabChange }: ProductEditorTabsProps) {
  return (
    <div className="border-b border-admin-border">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === tab.id
                ? 'border-admin-primary text-admin-primary'
                : 'border-transparent text-admin-text-secondary hover:text-admin-text-primary hover:border-admin-border'
            }`}
          >
            <span className="text-base">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </nav>
    </div>
  )
}
