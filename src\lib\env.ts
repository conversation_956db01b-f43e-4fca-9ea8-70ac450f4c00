import { z } from 'zod'

const envSchema = z.object({
  VITE_SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  VITE_SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  VITE_APP_NAME: z.string().default('Womanza Admin Panel'),
  VITE_APP_VERSION: z.string().default('1.0.0'),
  VITE_APP_ENVIRONMENT: z.enum(['development', 'staging', 'production']).default('development'),
  VITE_API_BASE_URL: z.string().url().optional(),
  VITE_STORE_ID: z.string().default('womanza-store'),
  VITE_STORE_NAME: z.string().default('Womanza Store'),
  VITE_STORE_DOMAIN: z.string().default('womanza.com'),
  VITE_ENABLE_ANALYTICS: z.string().transform(val => val === 'true').default('true'),
  VITE_ENABLE_REAL_TIME: z.string().transform(val => val === 'true').default('true'),
  VITE_ENABLE_USER_INVITES: z.string().transform(val => val === 'true').default('true'),
  VITE_ENABLE_EMAIL_CAMPAIGNS: z.string().transform(val => val === 'true').default('true'),
  VITE_ENABLE_ADVANCED_REPORTING: z.string().transform(val => val === 'true').default('true'),
  VITE_SESSION_TIMEOUT: z.string().transform(val => parseInt(val, 10)).default('3600000'),
  VITE_INVITE_EXPIRY_DAYS: z.string().transform(val => parseInt(val, 10)).default('7'),
  VITE_DEBUG_MODE: z.string().transform(val => val === 'true').default('false'),
  VITE_LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
})

function validateEnv() {
  try {
    return envSchema.parse(import.meta.env)
  } catch (error) {
    console.error('❌ Invalid environment variables:', error)
    throw new Error('Invalid environment configuration')
  }
}

export const env = validateEnv()

export type Env = z.infer<typeof envSchema>
